import { IDate, ITimezone } from '../helpers';
import { SERVICES } from '../utils';
import { IObjectText, IUserLocation, Maybe } from './index';

export type INameText = {
  name?: string;
  text?: IObjectText;
};

export enum HousekeepingProgressPostTaskType {
  Step1 = 1,
  Step2 = 2,
  Step3 = 3,
  Step4 = 4,
  Step5 = 5,
}

export enum NameHomeTypeHousekeeping {
  apartment = 'APARTMENT',
  house = 'HOME',
}

export enum NameOptionByHomeTypeHousekeeping {
  stairsTransport = 'stairsTransport',
  garage = 'garage',
  byroad = 'byroad',
  elevatorTransport = 'elevatorTransport',
}

export enum NameTypeHouseHomeTypeHousekeeping {
  groundFloor = 'groundFloor',
}

export enum NameOptionTypeFurnitureHousekeeping {
  airConditioner = 'airConditioner',
  waterHeater = 'waterHeater',
}

export enum NameOptionTypeWaterHeaterHousekeeping {
  traditionalWaterHeater = 'traditionalWaterHeater',
  tanklessWaterHeater = 'tanklessWaterHeater',
}

export enum NameTypeFurnitureHousekeeping {
  electronic = 'electronic',
  solidFurniture = 'solidFurniture',
  removableFurniture = 'removableFurniture',
}

export enum TypeLocationHousekeeping {
  New = 'NEW',
  Old = 'OLD',
}

export type IHousekeeping = {
  city?: ISettingHousekeepingServiceByCity[];
};

export type ISettingHousekeepingServiceByCity = {
  name?: string;
  homeType?: IHomeTypeHousekeeping[];
  options?: IOptionHomeTypeHousekeeping[];
  furniture?: IFurnitureHousekeeping;
};

export type IOptionHomeTypeHousekeeping = {
  name?: NameOptionByHomeTypeHousekeeping;
  text?: IObjectText;
  options?: INameText[];
};

export type IFurnitureHousekeeping = {
  name?: NameTypeFurnitureHousekeeping;
  text?: IObjectText;
  options?: IOptionTypeFurnitureHousekeeping[];
};

export type IOptionTypeFurnitureHousekeeping = {
  name?: NameOptionTypeFurnitureHousekeeping;
  text?: IObjectText;
  options?: INameText[];
};

export type IDetailHousekeeping = {
  isInBuilding?: boolean;
  stairsTransportStep?: number;
  byroad?: number;
  garage?: boolean;
  elevatorTransport?: boolean;
};

export type IHomeTypeHousekeeping = {
  name?: NameHomeTypeHousekeeping;
  isCanMoveInBuilding?: boolean;
  options?: NameOptionByHomeTypeHousekeeping[];
  text?: IObjectText;
  type?: TypeHomeTypeHousekeeping[];
};

export type TypeHomeTypeHousekeeping = {
  name?: NameTypeHouseHomeTypeHousekeeping;
  price?: number;
  text?: IObjectText;
  description?: IObjectText;
  vehicleQuantity?: number;
  vehicleType?: string;
  options?: OptionTypeHomeTypeHousekeeping[];
  oldHomeCleaning?: IHomeCleaning;
  newHomeCleaning?: IHomeCleaning;
};

type IHomeCleaning = {
  serviceName?: SERVICES;
  numberOfTasker?: number;
  duration?: number;
  bookTaskAfterHours?: number;
};

export type OptionTypeHomeTypeHousekeeping = {
  name?: NameOptionByHomeTypeHousekeeping;
  text?: IObjectText;
  price?: number;
  description?: IObjectText;
};

export type IFurnitureItem = {
  name?: NameTypeFurnitureHousekeeping;
  text?: IObjectText;
  quantity?: number;
  images?: string[];
  options?: IFurnitureItemOption[];
};

export type IFurnitureItemOption = {
  name?: NameOptionTypeFurnitureHousekeeping;
  text?: IObjectText;
  quantity?: number;
  options?: IFurnitureItemOptionDetail[];
};

export type IFurnitureItemOptionDetail = {
  name?: NameOptionTypeWaterHeaterHousekeeping;
  text?: IObjectText;
  quantity?: number;
};

export type IHousekeepingPostTaskData = {
  service?: {
    name?: SERVICES;
  };
  oldHomeDetail?: {
    address?: IUserLocation;
    homeType?: IHomeTypeHousekeeping;
    detail?: IDetailHousekeeping;
  };
  newHomeDetail?: {
    address?: IUserLocation;
    homeType?: IHomeTypeHousekeeping;
    detail?: IDetailHousekeeping;
  };
  furniture?: IFurnitureItem[];
  date?: IDate;
  timezone?: ITimezone;
  note?: string;
};
