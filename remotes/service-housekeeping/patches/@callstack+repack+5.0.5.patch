diff --git a/node_modules/@callstack/repack/dist/commands/common/logo.js b/node_modules/@callstack/repack/dist/commands/common/logo.js
index b349a1a..6a7cc38 100644
--- a/node_modules/@callstack/repack/dist/commands/common/logo.js
+++ b/node_modules/@callstack/repack/dist/commands/common/logo.js
@@ -10,9 +10,11 @@ function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e
 function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
 function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
 const logoStr = `
-▄▀▀▀ ▀▀▀▀   █▀▀█ █▀▀█ ▄▀▀▀ █  █
-█    ▀▀▀▀   █▀▀▀ █▀▀█ █    █▀▀▄
-▀    ▀▀▀▀ ▀ ▀    ▀  ▀  ▀▀▀ ▀  ▀`;
+███    ███  ██████  ██████  ██ ██      ███████ 
+████  ████ ██    ██ ██   ██ ██ ██      ██      
+██ ████ ██ ██    ██ ██████  ██ ██      █████   
+██  ██  ██ ██    ██ ██   ██ ██ ██      ██      
+██      ██  ██████  ██████  ██ ███████ ██████`;
 function logo(version, bundler) {
   const gradientLogo = (0, _gradientString.default)([{
     color: '#9b6dff',
@@ -21,6 +23,6 @@ function logo(version, bundler) {
     color: '#3ce4cb',
     pos: 0.9
   }]).multiline(logoStr);
-  return `${gradientLogo}\n${version}, powered by ${colorette.bold(bundler)}\n\n`;
+  return `${gradientLogo}\n${version}, powered by bTaskee\n\n`;
 }
 //# sourceMappingURL=logo.js.map
\ No newline at end of file
diff --git a/node_modules/@callstack/repack/src/cli/logo.ts b/node_modules/@callstack/repack/src/cli/logo.ts
new file mode 100644
index 0000000..e69de29
