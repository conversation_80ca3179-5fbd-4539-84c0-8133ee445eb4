import {
  <PERSON><PERSON>,
  DateTimeHelpers,
  getDurationByCountry,
  IAddress,
  IDate,
  Requirement,
} from '@btaskee/design-system';
import { usePostTaskStore } from '@store';
import { includes } from 'lodash-es';

import { useI18n } from '@hooks';

import { usePostTask } from './usePostTask';

export const useChangeData = () => {
  const { t } = useI18n();
  const {
    setDateTime,
    duration,
    setDuration,
    setRequirements,
    requirements,
    isPremium,
    setIsPremium,
    setIsAutoChooseTasker,
    isAutoChooseTasker,
    setPet,
  } = usePostTaskStore();
  const { getPrice } = usePostTask();

  const checkLimitDuration = (newDuration: number) => {
    const DURATIONS = getDurationByCountry();
    const lastDurationObj = DURATIONS[DURATIONS.length - 1];
    if (newDuration > lastDurationObj?.duration) {
      Alert.alert?.open?.({
        title: t('DIALOG_TITLE_INFORMATION'),
        message: t('LIMIT_DURATION', { t: lastDurationObj?.duration }),
        actions: [{ text: t('CLOSE') }],
      });
      return true;
    }
    return false;
  };

  const onChangeAddOnService = (req: Requirement) => {
    const DURATIONS = getDurationByCountry();
    let newService = [...requirements];

    let newDuration = duration || DURATIONS[0].duration;
    const durationFromService = req?.duration || 0;

    // check exist
    if (newService.find((e) => e.type === req.type)) {
      newService = newService.filter((e) => e.type !== req.type);
      // auto decrease duration
      newDuration -= durationFromService;
    } else {
      newService.push(req);
      // auto increase duration
      newDuration += durationFromService;
    }

    // check limit duration
    const isLimitDuration = checkLimitDuration(newDuration);
    if (isLimitDuration) {
      return;
    }

    // update requirement
    setRequirements(newService);
    setDuration(newDuration);
    // get price
    getPrice();
  };

  const onChangePremiumService = (value: boolean) => {
    setIsPremium(value);
    // get price
    getPrice();
  };

  const onChangeDuration = (newDuration: number) => {
    setDuration(newDuration);
    // reset addOnService
    setRequirements([]);
    // get price
    getPrice();
  };

  const checkPremiumOption = ({
    citiesApplyPremium = [],
    address,
  }: {
    citiesApplyPremium: string[];
    address: IAddress;
  }) => {
    // check premium
    if (!includes(citiesApplyPremium, address?.city) && isPremium) {
      // set isPremium
      setIsPremium(false);
    }
    // get price
    getPrice();
  };

  const onChangeAutoChooseTasker = () => {
    setIsAutoChooseTasker(!isAutoChooseTasker);
    // get price
    getPrice();
  };

  const setPetOption = (data: any) => {
    setPet(data);
    // get price
    getPrice();
  };

  const onChangeDateTime = (newDate: IDate) => {
    const currentState = usePostTaskStore.getState();
    const { address, date } = currentState;

    const timezone = DateTimeHelpers.getTimezoneByCity(address?.city);
    const isSame = DateTimeHelpers.checkIsSame({
      firstDate: date,
      secondDate: newDate,
      timezone,
    });
    // check spam, call api with same data
    if (isSame) return null;
    // set new date time
    setDateTime(newDate);
    // get price again
    getPrice();
  };

  return {
    onChangeAddOnService,
    onChangePremiumService,
    checkPremiumOption,
    onChangeDuration,
    onChangeAutoChooseTasker,
    setPetOption,
    onChangeDateTime,
  };
};
