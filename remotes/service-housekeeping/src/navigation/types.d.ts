import { NativeStackNavigationProp } from '@react-navigation/native-stack';

import { RouteName } from './RouteName';

export type RootStackParamList = {
  [RouteName.Home]: undefined;
  [RouteName.IntroService]: undefined;
  [RouteName.ChooseAddress]: undefined;
  [RouteName.ChooseService]: undefined;
  [RouteName.ChooseDateTime]: undefined;
  [RouteName.ConfirmAndPayment]: undefined;
  [RouteName.PostTaskSuccess]: undefined;
  [RouteName.ChooseDuration]: undefined;
  [RouteName.NotesForTasker]: undefined;
  [RouteName.WorkingDescriptionProgress]: undefined;
  [RouteName.StandardPackagingProcess]: undefined;
};

export type RootStackScreenProps<T extends keyof RootStackParamList> =
  NativeStackNavigationProp<RootStackParamList, T>;
