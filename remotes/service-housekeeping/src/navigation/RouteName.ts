export enum RouteName {
  Home = 'Housekeeping/Home',
  IntroService = 'Housekeeping/IntroService',
  ChooseAddress = 'Housekeeping/ChooseAddress',
  ChooseService = 'Housekeeping/ChooseService',
  ChooseDateTime = 'Housekeeping/ChooseDateTime',
  ConfirmAndPayment = 'Housekeeping/ConfirmAndPayment',
  PostTaskSuccess = 'Housekeeping/PostTaskSuccess',
  ChooseDuration = 'Housekeeping/ChooseDuration',
  NotesForTasker = 'Housekeeping/NotesForTasker',
  WorkingDescriptionProgress = 'Housekeeping/WorkingDescriptionProgress',
  StandardPackagingProcess = 'Housekeeping/StandardPackagingProcess',
}
