export enum RouteName {
  Home = 'HomeMoving/Home',
  IntroService = 'HomeMoving/IntroService',
  ChooseAddress = 'HomeMoving/ChooseAddress',
  ChooseService = 'HomeMoving/ChooseService',
  ChooseDateTime = 'HomeMoving/ChooseDateTime',
  ConfirmAndPayment = 'HomeMoving/ConfirmAndPayment',
  PostTaskSuccess = 'HomeMoving/PostTaskSuccess',
  ChooseDuration = 'HomeMoving/ChooseDuration',
  NotesForTasker = 'HomeMoving/NotesForTasker',
  WorkingDescriptionProgress = 'HomeMoving/WorkingDescriptionProgress',
  StandardPackagingProcess = 'HomeMoving/StandardPackagingProcess',
}
