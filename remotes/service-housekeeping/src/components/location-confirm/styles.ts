import { StyleSheet } from 'react-native';
import { ColorsV2, FontSizes, Spacing } from '@btaskee/design-system';

export default StyleSheet.create({
  txtAddress: {
    marginBottom: 5,
  },
  txtAlias: {
    marginBottom: 5,
    fontSize: FontSizes.SIZE_16,
  },
  txtInfoLocation: {
    color: ColorsV2.neutral500,
    marginBottom: 5,
  },
  iconChange: {
    width: 24,
    height: 24,
  },
  panel: {
    marginTop: Spacing.SPACE_24,
    marginBottom: Spacing.SPACE_16,
    justifyContent: 'space-between',
  },
  txtPanel: {
    fontSize: FontSizes.SIZE_16,
  },
  containerLocation: {
    flex: 1,
    paddingBottom: Spacing.SPACE_04,
    marginBottom: Spacing.SPACE_04,
  },
  wrapLocation: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: Spacing.SPACE_08,
  },
  wrapContact: {
    flex: 1,
    marginTop: Spacing.SPACE_08,
  },
  wrapNumberPhone: {
    flex: 1,
    flexDirection: 'row',
    marginTop: Spacing.SPACE_04,
  },
});
