import React, { createRef, useState } from 'react';
import {
  <PERSON><PERSON>iew,
  Card,
  CModal,
  CModalHandle,
  ColorsV2,
  CText,
  CTextInput,
  getTextWithLocale,
  HomeMovingProgressPostTaskType,
  SizedBox,
  Spacing,
  TypeLocationHomeMoving,
  validPhoneNumber,
} from '@btaskee/design-system';
import { usePostTaskStore } from '@store';

import { useI18n } from '@hooks';

import { AddressItem } from '../address-item';
import styles from './styles';

export const LocationConfirm = () => {
  const { t } = useI18n();

  const { address, oldHomeDetail, newHomeDetail, setHomeDetail, setAddress } =
    usePostTaskStore();

  const modalRef = createRef<CModalHandle>();
  const [typeContact, setTypeContact] = useState<TypeLocationHomeMoving | null>(
    null,
  );
  const [phone, setPhone] = useState(address.phoneNumber);
  const [name, setName] = useState(address.contact);

  const _openChangeContactModal = () => {
    modalRef?.current?.open && modalRef?.current?.open();
  };

  const _changeContactInfo = () => {
    if (typeContact === TypeLocationHomeMoving.New) {
      setHomeDetail({
        step: HomeMovingProgressPostTaskType.Step2,
        homeDetail: {
          ...newHomeDetail,
          addressDetail: {
            ...newHomeDetail?.addressDetail,
            contactName: name,
            phone,
          },
        },
      });
    }
    if (typeContact === TypeLocationHomeMoving.Old) {
      setHomeDetail({
        step: HomeMovingProgressPostTaskType.Step1,
        homeDetail: {
          ...oldHomeDetail,
          addressDetail: {
            ...oldHomeDetail?.addressDetail,
            contactName: name,
            phone,
          },
        },
      });
      setAddress({ ...address, contact: name, phoneNumber: phone });
    }
  };

  const handleOpenChangeContact = ({
    phoneNumber,
    contactName,
    type,
  }: {
    phoneNumber?: string;
    contactName?: string;
    type: TypeLocationHomeMoving;
  }) => {
    setPhone(phoneNumber);
    setName(contactName);
    _openChangeContactModal();
    setTypeContact(type);
  };

  if (!address) {
    return null;
  }

  return (
    <BlockView>
      <BlockView
        style={styles.panel}
        row
      >
        <CText
          bold
          style={styles.txtPanel}
        >
          {t('LOCATION')}
        </CText>
      </BlockView>
      <Card>
        <AddressItem
          key={'oldAddress'}
          title={t('PLACE_TO_MOVE')}
          shortAddress={oldHomeDetail?.addressDetail?.shortAddress}
          homeType={getTextWithLocale(oldHomeDetail?.homeType?.text)}
          contact={oldHomeDetail?.addressDetail?.contactName}
          phone={oldHomeDetail?.addressDetail?.phone}
          onPress={() => {
            handleOpenChangeContact({
              phoneNumber: oldHomeDetail?.addressDetail?.phone,
              contactName: oldHomeDetail?.addressDetail?.contactName,
              type: TypeLocationHomeMoving.Old,
            });
            // trackingServiceClick({
            //   screenName: TrackingScreenNames.ConfirmPayment,
            //   serviceName: SERVICES.HOME_MOVING,
            //   action: TRACKING_ACTION.ChangeInfoAtCurrentLocation,
            // });
          }}
        />
        <SizedBox height={Spacing.SPACE_16} />
        <SizedBox
          height={1}
          color={ColorsV2.neutral100}
        />
        <SizedBox height={Spacing.SPACE_16} />
        <AddressItem
          key={'newAddress'}
          title={t('PLACE_TO_MOVE_TO')}
          shortAddress={newHomeDetail?.addressDetail?.shortAddress}
          homeType={getTextWithLocale(newHomeDetail?.homeType?.text)}
          contact={newHomeDetail?.addressDetail?.contactName}
          phone={newHomeDetail?.addressDetail?.phone}
          onPress={() => {
            handleOpenChangeContact({
              phoneNumber: newHomeDetail?.addressDetail?.phone,
              contactName: newHomeDetail?.addressDetail?.contactName,
              type: TypeLocationHomeMoving.New,
            });
            // trackingServiceClick({
            //   screenName: TrackingScreenNames.ConfirmPayment,
            //   serviceName: SERVICES.HOME_MOVING,
            //   action: TRACKING_ACTION.ChangeInfoAtNewLocation,
            // });
          }}
        />
      </Card>
      <CModal
        ref={modalRef}
        avoidKeyboard
        title={t('CONTACT_INFO_MODAL_TITLE')}
        actions={[
          {
            text: t('UPDATE'),
            disabled: !name || !validPhoneNumber(phone, address?.countryCode),
            onPress: () => _changeContactInfo(),
          },
        ]}
      >
        <BlockView>
          <CTextInput
            testID="contactPhoneNumber"
            defaultValue={phone}
            countryCode={address.countryCode}
            validType="phone"
            label={t('PHONE_NUMBER').toUpperCase()}
            onChangeText={(text: string) => setPhone(text.trim())}
            maxLength={12}
          />
          <CTextInput
            testID="contactName"
            defaultValue={name}
            label={t('CONTACT_NAME').toUpperCase()}
            onChangeText={(text: string) => setName(text.trim())}
            maxLength={40}
          />
        </BlockView>
      </CModal>
    </BlockView>
  );
};
