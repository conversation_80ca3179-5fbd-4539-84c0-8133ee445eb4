import React, { ReactNode } from 'react';
import {
  BlockView,
  ColorsV2,
  ConditionView,
  CText,
  FontSizes,
  Spacing,
} from '@btaskee/design-system';

type SectionProps = {
  title: string;
  subTitle?: string;
  leftTitle?: ReactNode;
  children?: ReactNode;
  margin?: {
    top?: number;
    bottom?: number;
    left?: number;
    right?: number;
    horizontal?: number;
    vertical?: number;
  };
  padding?: {
    top?: number;
    bottom?: number;
    left?: number;
    right?: number;
    horizontal?: number;
    vertical?: number;
  };
};

export const Section = ({
  title,
  subTitle,
  margin,
  padding,
  leftTitle,
  children,
}: SectionProps) => {
  return (
    <BlockView
      margin={margin}
      padding={padding}
    >
      <BlockView
        row
        justify="space-between"
        align="center"
      >
        <CText
          bold
          size={FontSizes.SIZE_18}
          color={ColorsV2.neutral800}
        >
          {title}
        </CText>
        {leftTitle}
      </BlockView>

      <ConditionView
        condition={Boolean(subTitle)}
        viewTrue={
          <CText
            margin={{ top: Spacing.SPACE_04 }}
            size={FontSizes.SIZE_14}
            color={ColorsV2.neutral600}
          >
            {subTitle}
          </CText>
        }
      />
      {children}
    </BlockView>
  );
};
