/**
 * <AUTHOR>
 * @email [<EMAIL>]
 * @create date 2024-02-23 17:56:48
 * @modify date 2024-02-23 17:56:48
 * @desc [Working time]
 */

import React from 'react';
import {
  BlockView,
  CText,
  DateWithGMT,
  IDate,
  ITimezone,
  Maybe,
  RowInfo,
  TypeFormatDate,
} from '@btaskee/design-system';
import { usePostTaskStore } from '@store';

import { useI18n } from '@hooks';

import styles from './styles';

const WorkingTimeItem = ({
  testID,
  title,
  date,
  timezone,
}: {
  testID?: string;
  title: string;
  date?: Maybe<IDate>;
  timezone: Maybe<ITimezone>;
}) => {
  if (!date) {
    return null;
  }
  return (
    <RowInfo
      testID={testID}
      label={title}
      value={
        <DateWithGMT
          timezone={timezone}
          date={date}
          typeFormat={TypeFormatDate.DateTimeFullWithDay}
        />
      }
    />
  );
};

export const WorkingTime = () => {
  const { t } = useI18n();

  const { date, oldHomeDetail, newHomeDetail, timezones } = usePostTaskStore();

  return (
    <BlockView>
      <CText
        bold
        style={styles.subPanel}
      >
        {t('TIME_TO_WORK')}
      </CText>
      <WorkingTimeItem
        testID="timeMovingTxt"
        title={t('TIME_MOVING')}
        date={date}
        timezone={timezones.oldHome}
      />
      <WorkingTimeItem
        testID="timeCleaningOldHomeDetailTxt"
        title={t('CLEANING_OLD_HOME_DATE')}
        date={oldHomeDetail?.date}
        timezone={timezones.oldHome}
      />
      <WorkingTimeItem
        testID="timeCleaningNewHomeDetailTxt"
        title={t('CLEANING_NEW_HOME_DATE')}
        date={newHomeDetail?.date}
        timezone={timezones.newHome}
      />
    </BlockView>
  );
};
