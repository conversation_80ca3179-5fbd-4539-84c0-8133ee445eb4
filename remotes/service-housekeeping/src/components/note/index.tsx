import React from 'react';
import { BlockView, Card, CText } from '@btaskee/design-system';
import { usePostTaskStore } from '@store';

import { useI18n } from '@hooks';

import styles from './styles';

export const TaskNotes = () => {
  const { t } = useI18n();

  const { note } = usePostTaskStore();

  if (!note) {
    return null;
  }

  return (
    <BlockView>
      <BlockView style={styles.panel}>
        <CText
          bold
          style={styles.txtPanel}
        >
          {t('LABEL_NOTE_FOR_TASKER')}
        </CText>
      </BlockView>
      <Card>
        <CText testID={'taskNote'}>{note}</CText>
      </Card>
    </BlockView>
  );
};
