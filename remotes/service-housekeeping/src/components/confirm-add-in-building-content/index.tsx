import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>View,
  ColorsV2,
  CText,
  FastImage,
  FontSizes,
  PrimaryButton,
  SizedBox,
  Spacing,
} from '@btaskee/design-system';

import { useI18n } from '@hooks';
import { imgSameBuilding } from '@images';

import { styles } from './styles';

type ConfirmAddInBuildingTypeContentProps = {
  onConfirm?: () => void;
};

export const ConfirmAddInBuildingTypeContent = ({
  onConfirm,
}: ConfirmAddInBuildingTypeContentProps) => {
  const { t } = useI18n();

  const confirm = () => {
    close();
    onConfirm?.();
  };

  const close = () => {
    Alert.alert?.close?.();
  };

  return (
    <BlockView
      testID="confirmAddInBuildingView"
      style={styles.container}
    >
      <BlockView style={styles.content}>
        <BlockView
          center
          width={'100%'}
          margin={{ bottom: Spacing.SPACE_24 }}
        >
          <FastImage
            source={imgSameBuilding}
            style={styles.image}
            resizeMode="contain"
          />
        </BlockView>
        <CText
          size={FontSizes.SIZE_14}
          color={ColorsV2.neutral800}
          style={{ marginBottom: Spacing.SPACE_08 }}
        >
          {t('TRANSPORTATION_IN_THE_SAME_BUILDING_DES_1')}
        </CText>
        <CText
          italic
          size={FontSizes.SIZE_14}
          color={ColorsV2.neutral600}
          style={{ marginBottom: Spacing.SPACE_16 }}
        >
          {t('TRANSPORTATION_IN_THE_SAME_BUILDING_DES_2')}
        </CText>
        <CText
          size={FontSizes.SIZE_14}
          color={ColorsV2.neutral800}
        >
          {t('TRANSPORTATION_IN_THE_SAME_BUILDING_DES_3')}
        </CText>
      </BlockView>
      <BlockView
        row
        style={{ marginTop: Spacing.SPACE_24 }}
      >
        <PrimaryButton
          type="secondary"
          title={t('CLOSE')}
          style={styles.button}
          onPress={close}
        />
        <SizedBox width={Spacing.SPACE_16} />
        <PrimaryButton
          testID="confirmAddInBuildingButton"
          title={t('CONFIRM')}
          style={styles.button}
          onPress={confirm}
        />
      </BlockView>
    </BlockView>
  );
};
