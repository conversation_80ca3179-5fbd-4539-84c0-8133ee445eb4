import React, { useMemo, useState } from 'react';
import { ImageProps } from 'react-native';
import {
  AnimationHelpers,
  BlockView,
  ColorsV2,
  ConditionView,
  CText,
  FontSizes,
  HitSlop,
  Icon,
  IconImage,
  Maybe,
  Spacing,
  Switch,
  TouchableOpacity,
} from '@btaskee/design-system';

import { ChangeQuantityItem } from '../change-quantity-item';
import { styles } from './styles';

// Import ChangeQuantityItem from the original location for now
// This should be migrated to design system or created locally
type ChangeQuantityItemProps = {
  testIDs?: {
    value?: string;
    minus?: string;
    plus?: string;
  };
  title?: string;
  value?: number;
  unit?: string;
  min?: number;
  max?: number;
  step?: number;
  onChange?: (value: number) => void;
};

export type OptionItemProps<T> = {
  testIDs?: {
    item?: string;
    value?: string;
  };
  label: string;
  sourceIcon: ImageProps['source'];
  value?: boolean | string;
  options?: T[];
  titleOptions?: string;
  noteOptions?: string;
  isExpand?: boolean;
  optionAsLabel?: (option: T) => string;
  optionAsSelected?: (option: T) => boolean;
  optionAsOnPress?: (option: T) => void;
  changeQuantityItemProps?: Maybe<ChangeQuantityItemProps>;
  onValueChange?: (value: boolean) => void;
};

type ItemProps = {
  label?: string;
  onPress?: () => void;
  isActive?: boolean;
};

const Item = ({ label, onPress, isActive }: ItemProps) => {
  const activeStyle = useMemo(() => {
    if (isActive) {
      return {
        backgroundColor: ColorsV2.orange50,
        borderColor: ColorsV2.orange500,
      };
    }
    return {};
  }, [isActive]);

  return (
    <TouchableOpacity
      onPress={onPress}
      style={[styles.itemContainer, activeStyle]}
    >
      <CText
        size={FontSizes.SIZE_14}
        color={isActive ? ColorsV2.orange500 : ColorsV2.neutral800}
      >
        {label}
      </CText>
    </TouchableOpacity>
  );
};

export const OptionItem = <T,>({
  testIDs,
  options,
  label,
  value,
  sourceIcon,
  titleOptions,
  noteOptions,
  isExpand,
  onValueChange,
  optionAsLabel,
  optionAsSelected,
  optionAsOnPress,
  changeQuantityItemProps,
}: OptionItemProps<T>) => {
  const [isCollapsible, setIsCollapsible] = useState(!isExpand);

  const onPress = () => {
    if (options?.length) {
      toggleCollapsible();
    } else {
      onValueChange?.(!value);
    }
  };

  const toggleCollapsible = () => {
    AnimationHelpers.runLayoutAnimation();
    setIsCollapsible(!isCollapsible);
  };

  const activeStyle = useMemo(() => {
    const isActive = Boolean(value) && isCollapsible;

    if (isActive) {
      return {
        backgroundColor: ColorsV2.orange50,
        borderColor: ColorsV2.orange500,
      };
    }
    return {};
  }, [value, isCollapsible]);

  return (
    <BlockView style={[styles.container, activeStyle]}>
      <TouchableOpacity
        testID={testIDs?.item}
        hitSlop={HitSlop.MEDIUM}
        onPress={onPress}
        style={styles.headerContainer}
      >
        <IconImage
          source={sourceIcon}
          size={Spacing.SPACE_24}
        />
        <BlockView
          row
          flex
          align="center"
          padding={{ horizontal: Spacing.SPACE_12 }}
        >
          <CText
            margin={{ right: Spacing.SPACE_08 }}
            size={FontSizes.SIZE_14}
            color={ColorsV2.neutral800}
          >
            {label}
          </CText>
        </BlockView>
        <ConditionView
          condition={
            Boolean(value) && typeof value === 'string' && isCollapsible
          }
          viewTrue={
            <BlockView style={styles.textValueContainer}>
              <CText
                testID={testIDs?.value}
                color={ColorsV2.orange500}
                size={FontSizes.SIZE_14}
              >
                {value}
              </CText>
            </BlockView>
          }
          viewFalse={
            <ConditionView
              condition={Boolean(options?.length)}
              viewTrue={
                <Icon
                  name={isCollapsible ? 'icArrowDown' : 'icArrowUp'}
                  size={Spacing.SPACE_20}
                  color={ColorsV2.neutral400 as any}
                />
              }
              viewFalse={
                <Switch
                  testID={testIDs?.value}
                  value={Boolean(value)}
                  onValueChange={onValueChange}
                />
              }
            />
          }
        />
      </TouchableOpacity>
      <ConditionView
        condition={!isCollapsible}
        viewTrue={
          <BlockView style={styles.contentContainer}>
            <ConditionView
              condition={Boolean(changeQuantityItemProps)}
              viewTrue={
                <BlockView padding={{ bottom: Spacing.SPACE_16 }}>
                  <ChangeQuantityItem {...changeQuantityItemProps} />
                </BlockView>
              }
            />
            <ConditionView
              condition={Boolean(titleOptions)}
              viewTrue={
                <CText
                  size={FontSizes.SIZE_14}
                  color={ColorsV2.neutral800}
                  // style={{ marginBottom: Spacing.SPACE_08 }}
                >
                  {titleOptions}
                </CText>
              }
            />
            <BlockView
              row
              wrap
              justify="space-between"
            >
              {options?.map((e, i) => {
                return (
                  <Item
                    key={i.toString()}
                    label={optionAsLabel?.(e)}
                    isActive={optionAsSelected?.(e)}
                    onPress={() => {
                      toggleCollapsible();
                      optionAsOnPress?.(e);
                    }}
                  />
                );
              })}
            </BlockView>
            <ConditionView
              condition={Boolean(noteOptions)}
              viewTrue={
                <CText
                  color={ColorsV2.orange500}
                  margin={{ top: Spacing.SPACE_08 }}
                  italic
                  size={FontSizes.SIZE_12}
                >
                  {noteOptions}
                </CText>
              }
            />
          </BlockView>
        }
      />
    </BlockView>
  );
};
