import { StyleSheet } from 'react-native';
import { BorderRadius, ColorsV2, Spacing } from '@btaskee/design-system';

export const styles = StyleSheet.create({
  container: {
    borderWidth: 1,
    borderRadius: BorderRadius.RADIUS_08,
    borderColor: ColorsV2.neutral100,
    marginTop: Spacing.SPACE_16,
  },
  textValueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  itemContainer: {
    width: '48.5%',
    borderWidth: 1,
    marginTop: Spacing.SPACE_12,
    padding: Spacing.SPACE_12,
    borderRadius: BorderRadius.RADIUS_08,
    backgroundColor: ColorsV2.neutral50,
    borderColor: ColorsV2.neutral100,
  },
  contentContainer: {
    borderTopWidth: 1,
    borderTopColor: ColorsV2.neutral100,
    marginRight: Spacing.SPACE_12,
    marginLeft: Spacing.SPACE_12,
    marginBottom: Spacing.SPACE_12,
    paddingTop: Spacing.SPACE_12,
  },
  headerContainer: {
    padding: Spacing.SPACE_12,
    flexDirection: 'row',
    alignItems: 'center',
  },
});
