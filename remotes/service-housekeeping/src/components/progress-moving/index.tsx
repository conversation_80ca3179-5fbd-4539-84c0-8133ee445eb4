import React, { useEffect, useMemo, useRef } from 'react';
import { Animated } from 'react-native';
import {
  BlockView,
  ConfigHel<PERSON>,
  DeviceHelper,
  HomeMovingProgressPostTaskType,
  IconAssets,
  Spacing,
} from '@btaskee/design-system';
import { usePostTaskStore } from '@store';

import { usePostTaskHomeMoving } from '@hooks';

import { ProgressItem } from '../progress-item';
import { styles } from './styles';

// Progress configuration - using IconAssets from design system
const LIST_PROGRESS_POST_TASK_HOME_MOVING = [
  {
    step: HomeMovingProgressPostTaskType.Step1,
    icon: IconAssets.icLocation,
  },
  {
    step: HomeMovingProgressPostTaskType.Step2,
    icon: IconAssets.icMoving,
  },
  {
    step: HomeMovingProgressPostTaskType.Step3,
    icon: IconAssets.icBox,
  },
  {
    step: HomeMovingProgressPostTaskType.Step4,
    icon: IconAssets.icClock,
  },
];

const SIZE_PROGRESS_ITEM = 32;
const HEIGHT_LINE = 1;
const PADDING_HORIZONTAL = Spacing.SPACE_16;
const WIDTH_CONTAINER = DeviceHelper.WINDOW.WIDTH - PADDING_HORIZONTAL * 2;
const LENGTH_PROGRESS = LIST_PROGRESS_POST_TASK_HOME_MOVING.length;
const WIDTH_STEP = WIDTH_CONTAINER / LENGTH_PROGRESS + SIZE_PROGRESS_ITEM;
const LINE_TRANSLATE_X_DEFAULT = -WIDTH_CONTAINER - SIZE_PROGRESS_ITEM / 2;

export const ProgressMoving = () => {
  const { onPressStep } = usePostTaskHomeMoving();

  // Use Zustand store instead of Redux
  const currentStep = usePostTaskStore((state) => state.currentStep);
  const passStep = usePostTaskStore((state) => state.passStep);

  const lineTranslateXtoValue = useMemo(() => {
    if (passStep <= LENGTH_PROGRESS) {
      return LINE_TRANSLATE_X_DEFAULT + (passStep * WIDTH_STEP - WIDTH_STEP);
    }
    return 0;
  }, [passStep]);

  const lineTranslateX = useRef(
    new Animated.Value(lineTranslateXtoValue),
  ).current;

  useEffect(() => {
    const duration = ConfigHelpers.isE2ETesting ? 0 : 500;

    Animated.timing(lineTranslateX, {
      toValue: lineTranslateXtoValue,
      duration: 500,
      useNativeDriver: true,
    }).start();
  }, [lineTranslateX, lineTranslateXtoValue, passStep]);

  return (
    <BlockView
      testID="progressMovingContainer"
      style={[styles.container, { paddingHorizontal: PADDING_HORIZONTAL }]}
    >
      <BlockView>
        <BlockView
          row
          zIndex={2}
          justify="space-between"
        >
          {LIST_PROGRESS_POST_TASK_HOME_MOVING.map((item, index) => {
            const isActive = item.step === currentStep;
            const isPass = item.step <= passStep;
            const isFirst = index === 0;
            return (
              <ProgressItem
                key={`progress-item-${item.step}`}
                sourceIcon={item.icon}
                isActive={isActive}
                isPass={isPass}
                isHideLine={isFirst}
                onPress={() => onPressStep?.(item.step)}
                size={SIZE_PROGRESS_ITEM}
              />
            );
          })}
        </BlockView>
        <BlockView style={styles.solidLineContainer}>
          <Animated.View
            style={[
              styles.solidLine,
              {
                width: WIDTH_CONTAINER,
                top: SIZE_PROGRESS_ITEM / 2 - HEIGHT_LINE / 2,
                transform: [
                  {
                    translateX: lineTranslateX,
                  },
                ],
              },
            ]}
          />
        </BlockView>
      </BlockView>
    </BlockView>
  );
};
