import React from 'react';
import { TouchableOpacity } from 'react-native';
import {
  BlockView,
  ColorsV2,
  ConditionView,
  CText,
  FontSizes,
  getTextWithLocale,
  HitSlop,
  IconAssets,
  IconImage,
  IHomeDetail,
  Maybe,
  SizedBox,
  Spacing,
  useAppStore,
} from '@btaskee/design-system';
import isEmpty from 'lodash-es/isEmpty';

import { useI18n, usePostTaskHomeMoving } from '@hooks';
import { icHomeType } from '@images';

import { styles } from './styles';

export interface HomeDetailInfoProps {
  testIDs?: {
    shortAddress?: string;
    homeType?: string;
  };
  title: string;
  fontSizeTitle?: number;
  homeDetail?: Maybe<IHomeDetail>;
  isShowShortAddress?: boolean;
  isShowBorderBottom?: boolean;
  onEdit?: () => void;
}

export interface DetailInfoProps {
  testID?: string;
  value?: string | React.ReactNode;
  quantity?: number;
}

export const DetailInfo: React.FC<DetailInfoProps> = ({
  testID,
  value,
  quantity,
}) => {
  const quantityTxt = ` x${quantity}`;

  return (
    <CText
      testID={testID}
      style={styles.txt_info}
    >
      {typeof value === 'string' ? value?.trim() : value}
      <ConditionView
        condition={Boolean(quantity)}
        viewTrue={
          <CText
            bold
            color={ColorsV2.green500}
          >
            {quantityTxt}
          </CText>
        }
      />
    </CText>
  );
};

export const HomeDetailInfo: React.FC<HomeDetailInfoProps> = ({
  testIDs,
  title,
  fontSizeTitle = FontSizes.SIZE_16,
  homeDetail,
  isShowShortAddress,
  isShowBorderBottom,
  onEdit,
}) => {
  const { t } = useI18n();
  const { locale } = useAppStore();
  const { getAcreageText } = usePostTaskHomeMoving();

  // Null checks and safe data extraction
  const homeType = homeDetail?.homeType?.text
    ? getTextWithLocale(homeDetail.homeType.text, locale)
    : '';
  const acreage = homeDetail ? getAcreageText(homeDetail) : '';
  const options = homeDetail?.options || [];
  const shortAddress = homeDetail?.addressDetail?.address || '';
  const typeTxt = acreage ? `${homeType} (${acreage})` : homeType;

  return (
    <BlockView
      padding={{ vertical: Spacing.SPACE_08 }}
      border={{
        bottom: {
          width: isShowBorderBottom ? 1 : 0,
          color: ColorsV2.neutral100,
        },
      }}
    >
      <SizedBox height={Spacing.SPACE_08} />

      <BlockView
        row
        justify="space-between"
        align="center"
      >
        <CText
          bold
          color={ColorsV2.orange500}
          size={fontSizeTitle}
          testID="home-detail-title"
        >
          {title}
        </CText>

        <ConditionView
          condition={Boolean(onEdit)}
          viewTrue={
            <TouchableOpacity
              hitSlop={HitSlop.MEDIUM}
              onPress={onEdit}
              testID="edit-button"
            >
              <IconImage
                source={IconAssets.icChange}
                color={ColorsV2.green500}
                size={20}
              />
            </TouchableOpacity>
          }
        />
      </BlockView>

      <ConditionView
        condition={Boolean(shortAddress && isShowShortAddress)}
        viewTrue={
          <CText
            testID={testIDs?.shortAddress}
            bold
            margin={{ vertical: Spacing.SPACE_08 }}
            color={ColorsV2.neutral800}
          >
            {shortAddress}
          </CText>
        }
      />

      <BlockView
        row
        style={styles.containerItem}
      >
        <IconImage
          source={icHomeType}
          size={20}
          color={ColorsV2.orange500}
        />
        <CText
          testID={testIDs?.homeType}
          color={ColorsV2.neutral800}
        >
          {typeTxt}
        </CText>
      </BlockView>

      <ConditionView
        condition={!isEmpty(options)}
        viewTrue={
          <BlockView
            row
            style={styles.containerItem}
          >
            <IconImage
              source={IconAssets.icSetting}
              size={20}
              color={ColorsV2.orange500}
            />
            <BlockView>
              {options?.map?.((item, index) => {
                let text = t('CONNECT_WORD', {
                  t1: getTextWithLocale(item?.text, locale),
                  t2: getTextWithLocale(item?.option?.text, locale),
                });
                if (item?.width) {
                  text += ` ${t('WIDTH_BYROAD', { width: item.width })}`;
                }
                return (
                  <DetailInfo
                    key={index.toString()}
                    value={text.trim()}
                    testID={`option-${index}`}
                  />
                );
              })}
            </BlockView>
          </BlockView>
        }
      />
    </BlockView>
  );
};
