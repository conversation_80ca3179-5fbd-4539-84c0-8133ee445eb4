import React from 'react';
import { BlockView, Card, CText } from '@btaskee/design-system';

import { useI18n } from '@hooks';

import { WorkingDetail } from '../working-detail';
import { WorkingTime } from '../working-time';
import { styles } from './styles';

export const TaskDetail = () => {
  const { t } = useI18n();

  return (
    <BlockView>
      <BlockView style={styles.panel}>
        <CText
          bold
          style={styles.txtPanel}
        >
          {t('TASK_INFO')}
        </CText>
      </BlockView>
      <Card>
        <WorkingTime />
        <WorkingDetail />
      </Card>
    </BlockView>
  );
};
