import React from 'react';
import {
  BlockView,
  ColorsV2,
  CText,
  FontSizes,
  IconAssets,
  IconImage,
  SizedBox,
  Spacing,
  TouchableOpacity,
} from '@btaskee/design-system';

import { useI18n } from '@hooks';

import styles from './styles';

interface IAddressItem {
  title?: string;
  shortAddress?: string;
  homeType?: string;
  contact?: string;
  phone?: string;
  onPress?: () => void;
}

export const AddressItem = ({
  title,
  shortAddress,
  homeType,
  contact,
  phone,
  onPress,
}: IAddressItem) => {
  const { t } = useI18n();

  return (
    <BlockView>
      <CText
        bold
        color={ColorsV2.orange500}
      >
        {title}
      </CText>
      <BlockView
        row
        margin={{ vertical: 8 }}
      >
        <IconImage
          source={IconAssets.icUser}
          color={ColorsV2.orange500}
        />
        <BlockView
          flex
          margin={{ horizontal: 8 }}
        >
          <CText
            bold
            size={FontSizes.SIZE_16}
          >
            {contact}
          </CText>
          <SizedBox height={8} />
          <CText>{phone}</CText>
        </BlockView>
        <BlockView>
          <TouchableOpacity
            onPress={onPress}
            style={styles.wrapBtn}
          >
            <CText
              bold
              size={FontSizes.SIZE_12}
              color={ColorsV2.neutralWhite}
            >
              {t('EDIT')}
            </CText>
          </TouchableOpacity>
        </BlockView>
      </BlockView>
      <SizedBox height={4} />
      <BlockView row>
        <BlockView>
          <IconImage
            source={IconAssets.icLocation}
            color={ColorsV2.orange500}
            size={24}
          />
        </BlockView>
        <BlockView
          flex
          margin={{ horizontal: Spacing.SPACE_08 }}
        >
          <CText
            bold
            size={FontSizes.SIZE_16}
          >
            {shortAddress}
          </CText>
          <SizedBox height={8} />
          <CText>{homeType}</CText>
        </BlockView>
      </BlockView>
    </BlockView>
  );
};
