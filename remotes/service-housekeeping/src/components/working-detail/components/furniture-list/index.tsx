import React from 'react';
import {
  BlockView,
  ColorsV2,
  CText,
  getTextWithLocale,
  IconImage,
  Spacing,
} from '@btaskee/design-system';
import { usePostTaskStore } from '@store';
import { isEmpty } from 'lodash-es';

import { DetailInfo } from '@components';
import { useI18n } from '@hooks';
import { icSofa } from '@images';

import { styles } from './styles';

export const FurnitureList = () => {
  const { t } = useI18n();
  const { furniture } = usePostTaskStore() || [];

  if (isEmpty(furniture)) {
    return null;
  }
  return (
    <BlockView
      padding={{ top: Spacing.SPACE_16 }}
      border={{ top: { width: 1, color: ColorsV2.neutral100 } }}
    >
      <CText
        bold
        color={ColorsV2.orange500}
      >
        {t('LABEL_OVER_FURNITURE')}
      </CText>
      <BlockView
        row
        style={styles.containerItem}
      >
        <IconImage source={icSofa} />
        <BlockView>
          {furniture?.map?.((item, index) => {
            return (
              <DetailInfo
                key={index.toString()}
                value={getTextWithLocale(item?.text)}
                quantity={item?.quantity}
              />
            );
          })}
        </BlockView>
      </BlockView>
    </BlockView>
  );
};
