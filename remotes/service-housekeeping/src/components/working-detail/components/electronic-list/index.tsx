import React from 'react';
import {
  BlockView,
  ColorsV2,
  CText,
  getTextWithLocale,
  IconImage,
  IOptionTypeFurnitureHomeMoving,
  NameTypeFurnitureHomeMoving,
  SizedBox,
  Spacing,
} from '@btaskee/design-system';
import { usePostTaskStore } from '@store';
import { isEmpty } from 'lodash-es';

import { DetailInfo } from '@components';
import { useI18n } from '@hooks';
import { icServices } from '@images';

import { styles } from './styles';

export const ElectronicList = () => {
  const { t } = useI18n();
  const { furniture } = usePostTaskStore() || [];

  const listElectronic = furniture?.find(
    (item) => item?.name === NameTypeFurnitureHomeMoving.electronic,
  )?.options;

  if (isEmpty(listElectronic)) {
    return null;
  }

  return (
    <BlockView margin={{ top: Spacing.SPACE_16 }}>
      <CText
        bold
        color={ColorsV2.orange500}
      >
        {t('INSTALL')}
      </CText>
      <BlockView
        row
        style={styles.containerItem}
      >
        <IconImage
          source={icServices}
          size={20}
        />
        <SizedBox width={Spacing.SPACE_08} />
        <BlockView>
          {listElectronic?.map((item: IOptionTypeFurnitureHomeMoving) => {
            return item?.options?.map((item2, index) => {
              return (
                <DetailInfo
                  key={index.toString()}
                  value={getTextWithLocale(item2?.text)}
                  quantity={item2.quantity}
                />
              );
            });
          })}
        </BlockView>
      </BlockView>
    </BlockView>
  );
};
