import React from 'react';
import {
  BlockView,
  ColorsV2,
  CText,
  FontSizes,
  SizedBox,
} from '@btaskee/design-system';
import { usePostTaskStore } from '@store';

import { useI18n } from '@hooks';

import { HomeDetailInfo } from '../home-detail-info';
import { ElectronicList } from './components/electronic-list';
import { FurnitureList } from './components/furniture-list';
import styles from './styles';

export const WorkingDetail = () => {
  const { t } = useI18n();

  const { oldHomeDetail, newHomeDetail } = usePostTaskStore();

  return (
    <BlockView>
      <BlockView style={styles.topBorder} />
      <CText
        bold
        style={styles.subPanel}
      >
        {t('TASK_INFO')}
      </CText>
      <HomeDetailInfo
        testIDs={{
          homeType: 'homeTypeTxtOldHomeDetailStep4',
        }}
        title={t('PLACE_TO_MOVE')}
        homeDetail={oldHomeDetail}
        fontSizeTitle={FontSizes.SIZE_14}
      />
      <SizedBox
        height={1}
        color={ColorsV2.neutral100}
      />
      <HomeDetailInfo
        testIDs={{
          homeType: 'homeTypeTxtNewHomeDetailStep4',
        }}
        title={t('PLACE_TO_MOVE_TO')}
        homeDetail={newHomeDetail}
        fontSizeTitle={FontSizes.SIZE_14}
      />
      <FurnitureList />

      <ElectronicList />
    </BlockView>
  );
};
