import React, { forwardRef, useImperativeHandle } from 'react';
import { TextStyle } from 'react-native';
import ImagePicker from 'react-native-image-crop-picker';
import {
  BlockView,
  BottomView,
  CModal,
  CModalHandle,
  ColorsV2,
  CText,
  <PERSON><PERSON>Helper,
  FontSizes,
  IconImage,
  PermissionsService,
  PrimaryButton,
  SizedBox,
  Spacing,
} from '@btaskee/design-system';

import { useI18n } from '@hooks';
import { imgCamera } from '@images';

import { styles } from './styles';

type UploadImageModalProps = {
  maxFiles?: number;
  onUploadImageSuccess?: (imageUrls?: string[]) => void;
  titleStyle?: TextStyle;
};

export type UploadImageModalHandle = {
  open: () => void;
  close: () => void;
};

/**
 * Modal component for uploading images from camera or photo library
 * Purpose: Provides user interface for selecting images through camera or gallery
 * @param maxFiles - Maximum number of files that can be selected from gallery
 * @param onUploadImageSuccess - Callback function called when images are successfully selected
 * @param titleStyle - Custom text style for the modal title
 * @returns {JSX.Element} Modal component with camera and gallery options
 */
const UploadImageModal = forwardRef<
  UploadImageModalHandle,
  UploadImageModalProps
>(({ maxFiles, onUploadImageSuccess, titleStyle = {} }, ref) => {
  const { t } = useI18n();
  const modalRef = React.useRef<CModalHandle>(null);

  useImperativeHandle(ref, () => ({
    open,
    close,
  }));

  /**
   * Opens the upload image modal
   * Purpose: Shows the modal to user for image selection
   * @returns {void} No return value, performs UI action
   */
  const open = (): void => {
    modalRef.current?.open();
  };

  /**
   * Closes the upload image modal
   * Purpose: Hides the modal from user interface
   * @returns {void} No return value, performs UI action
   */
  const close = (): void => {
    modalRef.current?.close();
  };

  /**
   * Handles camera image capture
   * Purpose: Opens device camera for taking photos with proper permissions
   * @returns {void} No return value, performs async camera operation
   */
  const _onOpenCamera = (): void => {
    close();
    setTimeout(() => {
      PermissionsService.checkRequestCamera({
        onGranted: () => {
          ImagePicker.openCamera({
            compressImageQuality: 0.3,
            mediaType: 'photo',
          }).then((image) => {
            const uri = image.path.startsWith('file://')
              ? image.path
              : `file://${image.path}`;
            onUploadImageSuccess?.([uri]);
          });
        },
      });
    }, 500);
  };

  /**
   * Handles photo library selection
   * Purpose: Opens device photo library for selecting multiple images
   * @returns {void} No return value, performs async gallery operation
   */
  const _onOpenPhotoLibrary = (): void => {
    close();
    setTimeout(() => {
      PermissionsService.checkRequestPhotoLibrary({
        onGranted: () => {
          ImagePicker.openPicker({
            multiple: true,
            maxFiles: maxFiles,
            mediaType: 'photo',
            compressImageQuality: 0.3,
          }).then((images) => {
            const newImages = images.map((image) => {
              const uri = image.path.startsWith('file://')
                ? image.path
                : `file://${image.path}`;
              return uri;
            });
            onUploadImageSuccess?.(newImages);
          });
        },
      });
    }, 500);
  };

  return (
    <CModal
      ref={modalRef}
      position="bottom"
      hideButtonClose
      contentContainerStyle={styles.container}
      title={t('TAKE_OR_DOWNLOAD_PHOTOS_FROM_GALLERY')}
    >
      <BlockView center>
        <BlockView
          row
          margin={{ bottom: Spacing.SPACE_24 }}
        >
          <IconImage
            source={imgCamera}
            size={DeviceHelper.WINDOW.WIDTH * 0.35}
          />
        </BlockView>
      </BlockView>
      <BottomView row>
        <PrimaryButton
          title={t('ALBUM')}
          type="secondary"
          style={styles.button}
          onPress={_onOpenPhotoLibrary}
        />
        <SizedBox width={Spacing.SPACE_16} />
        <PrimaryButton
          title={t('TAKE_A_PHOTO')}
          style={styles.button}
          onPress={_onOpenCamera}
        />
      </BottomView>
    </CModal>
  );
});

export { UploadImageModal };
