import { StyleSheet } from 'react-native';
import {
  ColorsV2,
  <PERSON><PERSON><PERSON><PERSON>per,
  FontSizes,
  Spacing,
} from '@btaskee/design-system';

export const styles = StyleSheet.create({
  modalContainer: {
    justifyContent: 'flex-end',
    margin: 0,
  },
  container: {
    backgroundColor: ColorsV2.neutralWhite,
    maxHeight: DeviceHelper.WINDOW.HEIGHT * 0.6,
  },
  txtStep: {
    color: ColorsV2.orange500,
    marginVertical: Spacing.SPACE_12,
    fontSize: FontSizes.SIZE_16,
  },
  wrapStep: {
    borderLeftWidth: 1,
    borderLeftColor: ColorsV2.green500,
    borderStyle: 'solid',
    paddingLeft: Spacing.SPACE_24,
    marginLeft: Spacing.SPACE_16,
  },
  numberStep: {
    backgroundColor: ColorsV2.green500,
    padding: Spacing.SPACE_04,
    borderRadius: 32,
    width: 32,
    height: 32,
    position: 'absolute',
    zIndex: 1,
    top: Spacing.SPACE_20,
  },
  txtNumberStep: {
    color: ColorsV2.neutralWhite,
  },
});
