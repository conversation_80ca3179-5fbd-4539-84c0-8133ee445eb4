import React from 'react';
import {
  BlockView,
  ColorsV2,
  ConditionView,
  CText,
  Markdown,
  ScrollView,
  SizedBox,
  Spacing,
} from '@btaskee/design-system';

import { useI18n } from '@hooks';

import { styles } from './styles';

type ProgressItemProps = {
  step?: number;
  title?: string;
  description?: string;
};

const ProgressItem = ({ title, description, step }: ProgressItemProps) => {
  return (
    <BlockView>
      <BlockView
        center
        style={styles.numberStep}
      >
        <CText style={styles.txtNumberStep}>{step}</CText>
      </BlockView>
      <BlockView style={styles.wrapStep}>
        <CText
          bold
          style={styles.txtStep}
        >
          {title}
        </CText>
        <SizedBox
          height={1}
          color={ColorsV2.neutral100}
        />
        <ConditionView
          condition={Boolean(description)}
          viewTrue={
            <BlockView
              flex
              margin={{ bottom: Spacing.SPACE_08 }}
            >
              <Markdown text={description} />
            </BlockView>
          }
        />
      </BlockView>
    </BlockView>
  );
};

type IState<T> = {
  items?: T[];
  itemAsDescription?: (item: T) => string;
};

export const ProgressModal = ({ items, itemAsDescription }: IState<any>) => {
  const { t } = useI18n();

  return (
    <BlockView style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {items?.map((item, index) => {
          const step = index + 1;
          return (
            <BlockView key={index}>
              <ProgressItem
                title={t('VALUE_STEP', { step })}
                description={itemAsDescription?.(item)}
                step={step}
              />
            </BlockView>
          );
        })}
      </ScrollView>
    </BlockView>
  );
};
