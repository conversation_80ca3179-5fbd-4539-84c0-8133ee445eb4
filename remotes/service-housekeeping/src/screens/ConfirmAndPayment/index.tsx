/**
 * ConfirmBooking Screen Component
 *
 * This screen allows users to review and confirm their massage service booking details,
 * including location, task details, payment method, and pricing information.
 * It handles regular and special (Tet holiday) booking flows with appropriate date selection options.
 */
import React, { useState } from 'react';
import { ScrollView } from 'react-native';
import {
  Alert,
  BlockView,
  BookingButton,
  CheckBox,
  ColorsV2,
  CText,
  CustomMarkDown,
  FontFamily,
  ISO_CODE,
  PAYMENT_METHOD,
  PaymentMethod,
  SizedBox,
  Spacing,
  useAppStore,
} from '@btaskee/design-system';
import { usePostTaskStore } from '@store';

import { LocationConfirm, TaskDetail, TaskNotes } from '@components';
import { useI18n, usePostTask } from '@hooks';

import { styles } from './styles';

/**
 * Props for the ConfirmBooking component
 */
interface ConfirmBookingProps {
  navigation: {
    navigate: (screen: string, params?: any) => void;
    popToTop: () => void;
  };
}

/**
 * ConfirmAndPayment component for reviewing and confirming massage service bookings
 * @param props - Component props
 */
export const ConfirmAndPayment: React.FC<ConfirmBookingProps> = ({
  navigation,
}) => {
  const { t } = useI18n();
  const { isoCode } = useAppStore();

  const { price, paymentMethod, promotion, setPromotion, setPaymentMethod } =
    usePostTaskStore();

  const { postTask } = usePostTask();
  const [isAcceptPolicy, setIsAcceptPolicy] = useState(false);

  return (
    <BlockView style={styles.container}>
      <ScrollView
        scrollIndicatorInsets={{ right: 1 }}
        testID="scrollViewStep4"
        contentContainerStyle={styles.content}
      >
        <LocationConfirm />
        <TaskDetail />
        <TaskNotes />

        <PaymentMethod
          paymentMethod={paymentMethod}
          promotion={promotion}
          setPromotion={setPromotion}
          setPaymentMethod={setPaymentMethod}
          blackList={[PAYMENT_METHOD.cash]}
        />

        <SizedBox height={Spacing.SPACE_32} />
        <BlockView row>
          <CheckBox
            testID="checkboxPolicyPostTaskHomeMoving"
            checked={isAcceptPolicy}
            onChecked={setIsAcceptPolicy}
            title={
              <BlockView
                width={'90%'}
                margin={{ left: Spacing.SPACE_12 }}
              >
                <CustomMarkDown
                  text={t('UNDERSTAND_POLICY')}
                  params={[
                    {
                      key: 't1',
                      value: t('MENU_TERM_OF_USE_TITLE').toLocaleLowerCase(),
                      style: {
                        color: ColorsV2.green500,
                        textDecorationLine: 'underline',
                        fontFamily: FontFamily.semiBold,
                      },
                      onPress: () => {
                        Alert.alert.open({
                          title: t('TITLE_POLICY'),
                          message: (
                            <BlockView
                              flex
                              padding={{ horizontal: Spacing.SPACE_16 }}
                            >
                              <ScrollView showsVerticalScrollIndicator={false}>
                                <CText lineHeight={24}>
                                  {isoCode === ISO_CODE.ID
                                    ? t('CONTENT_POLICY_ID')
                                    : t('CONTENT_POLICY')}
                                </CText>
                              </ScrollView>
                            </BlockView>
                          ),
                          actions: [{ text: t('UNDERSTOOD') }],
                        });
                      },
                    },
                  ]}
                />
              </BlockView>
            }
          />
        </BlockView>
      </ScrollView>
      <BookingButton
        isDisabled={!isAcceptPolicy}
        testID="btnSubmitPostTask"
        onPostTask={postTask}
        price={price}
        navigation={navigation}
      />
    </BlockView>
  );
};
