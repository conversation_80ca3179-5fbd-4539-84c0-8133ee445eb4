import { StyleSheet } from 'react-native';
import { ColorsV2, Devi<PERSON><PERSON>elper, Spacing } from '@btaskee/design-system';

export const styles = StyleSheet.create({
  container: {
    paddingBottom: Spacing.SPACE_32,
    backgroundColor: ColorsV2.neutralWhite,
  },
  imageHeader: {
    width: DeviceHelper.WINDOW.WIDTH,
    height: DeviceHelper.WINDOW.WIDTH * 0.5,
    marginBottom: Spacing.SPACE_16,
  },
  itemContainer: {
    flexDirection: 'row',
    padding: Spacing.SPACE_16,
    borderWidth: 1,
    borderColor: ColorsV2.neutral100,
    borderRadius: 8,
    alignItems: 'center',
    backgroundColor: ColorsV2.neutralWhite,
    marginHorizontal: Spacing.SPACE_16,

    shadowColor: ColorsV2.neutral400,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,

    elevation: 2,
  },
});
