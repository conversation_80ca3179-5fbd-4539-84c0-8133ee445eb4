import React from 'react';
import { FlatList, Image, TouchableOpacity } from 'react-native';
import {
  <PERSON><PERSON>,
  BlockView,
  ColorsV2,
  CText,
  IconAssets,
  IconImage,
  SizedBox,
  Spacing,
} from '@btaskee/design-system';

import { ProgressModal } from '@components';
import { useI18n } from '@hooks';
import {
  icPackagingProgress1,
  icPackagingProgress2,
  icPackagingProgress3,
  icPackagingProgress4,
  icPackagingProgress5,
  icPackagingProgress6,
  icPackagingProgress7,
  imgPackagingProgress,
} from '@images';

import { styles } from './styles';

export const StandardPackagingProcessScreen = () => {
  const { t } = useI18n();

  const DATA = [
    {
      icon: icPackagingProgress1,
      title: t('PACKAGING_PROGRESS_TITLE_1'),
      progress: [
        {
          description: t('PACKAGING_PROGRESS_TITLE_1_1'),
        },
        {
          description: t('PACKAGING_PROGRESS_TITLE_1_2'),
        },
        {
          description: t('PACKAGING_PROGRESS_TITLE_1_3'),
        },
        {
          description: t('PACKAGING_PROGRESS_TITLE_1_4'),
        },
      ],
    },
    {
      icon: icPackagingProgress2,
      title: t('PACKAGING_PROGRESS_TITLE_2'),
      progress: [
        {
          description: t('PACKAGING_PROGRESS_TITLE_2_1'),
        },
        {
          description: t('PACKAGING_PROGRESS_TITLE_2_2'),
        },
        {
          description: t('PACKAGING_PROGRESS_TITLE_2_3'),
        },
      ],
    },
    {
      icon: icPackagingProgress3,
      title: t('PACKAGING_PROGRESS_TITLE_3'),
      progress: [
        {
          description: t('PACKAGING_PROGRESS_TITLE_3_1'),
        },
        {
          description: t('PACKAGING_PROGRESS_TITLE_3_2'),
        },
      ],
    },
    {
      icon: icPackagingProgress4,
      title: t('PACKAGING_PROGRESS_TITLE_4'),
      progress: [
        {
          description: t('PACKAGING_PROGRESS_TITLE_4_1'),
        },
        {
          description: t('PACKAGING_PROGRESS_TITLE_4_2'),
        },
        {
          description: t('PACKAGING_PROGRESS_TITLE_4_3'),
        },
      ],
    },
    {
      icon: icPackagingProgress5,
      title: t('PACKAGING_PROGRESS_TITLE_5'),
      progress: [
        {
          description: t('PACKAGING_PROGRESS_TITLE_5_1'),
        },
        {
          description: t('PACKAGING_PROGRESS_TITLE_5_2'),
        },
        {
          description: t('PACKAGING_PROGRESS_TITLE_5_3'),
        },
      ],
    },
    {
      icon: icPackagingProgress6,
      title: t('PACKAGING_PROGRESS_TITLE_6'),
      progress: [
        {
          description: t('PACKAGING_PROGRESS_TITLE_6_1'),
        },
        {
          description: t('PACKAGING_PROGRESS_TITLE_6_2'),
        },
        {
          description: t('PACKAGING_PROGRESS_TITLE_6_3'),
        },
      ],
    },
    {
      icon: icPackagingProgress7,
      title: t('PACKAGING_PROGRESS_TITLE_7'),
      progress: [
        {
          description: t('PACKAGING_PROGRESS_TITLE_7_1'),
        },
        {
          description: t('PACKAGING_PROGRESS_TITLE_7_2'),
        },
      ],
    },
  ];

  const itemSeparator = () => <SizedBox height={Spacing.SPACE_16} />;
  const header = () => (
    <Image
      source={imgPackagingProgress}
      style={styles.imageHeader}
    />
  );

  return (
    <>
      <BlockView
        flex
        backgroundColor={ColorsV2.neutralWhite}
      >
        <FlatList
          data={DATA}
          showsVerticalScrollIndicator={false}
          keyExtractor={(_, index) => index.toString()}
          renderItem={({ item }) => {
            const onPress = () => {
              Alert.alert?.open({
                title: item.title,
                message: (
                  <ProgressModal
                    items={item.progress}
                    itemAsDescription={(e) => e.description}
                  />
                ),
                actions: [{ text: t('CLOSE') }],
              });
            };

            return (
              <TouchableOpacity
                style={styles.itemContainer}
                onPress={onPress}
              >
                <IconImage
                  source={item.icon}
                  size={48}
                />
                <SizedBox width={Spacing.SPACE_16} />
                <CText flex>{item.title}</CText>
                <SizedBox width={Spacing.SPACE_16} />
                <IconImage
                  source={IconAssets.icNext}
                  color={ColorsV2.neutral400}
                />
              </TouchableOpacity>
            );
          }}
          ItemSeparatorComponent={itemSeparator}
          ListHeaderComponent={header}
          contentContainerStyle={styles.container}
        />
      </BlockView>
    </>
  );
};
