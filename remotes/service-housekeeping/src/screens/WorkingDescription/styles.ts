import { StyleSheet } from 'react-native';
import { ColorsV2, FontSizes, Spacing } from '@btaskee/design-system';

export const styles = StyleSheet.create({
  container: {
    paddingHorizontal: Spacing.SPACE_16,
    paddingTop: Spacing.SPACE_08,
    paddingBottom: Spacing.SPACE_32,
    backgroundColor: ColorsV2.neutralWhite,
  },
  txtStep: {
    color: ColorsV2.orange500,
    marginVertical: Spacing.SPACE_08,
    fontSize: FontSizes.SIZE_16,
  },
  wrapStep: {
    borderLeftWidth: 1,
    borderLeftColor: ColorsV2.green500,
    borderStyle: 'solid',
    paddingLeft: Spacing.SPACE_32,
    marginLeft: Spacing.SPACE_16,
  },
  numberStep: {
    backgroundColor: ColorsV2.green500,
    padding: Spacing.SPACE_04,
    borderRadius: 32,
    width: 32,
    height: 32,
    position: 'absolute',
    zIndex: 1,
    top: Spacing.SPACE_20,
  },
  txtNumberStep: {
    color: ColorsV2.neutralWhite,
  },
});
