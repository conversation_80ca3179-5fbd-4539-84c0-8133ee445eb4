import React from 'react';
import {
  BlockView,
  ColorsV2,
  ConditionV<PERSON>w,
  CText,
  FlatList,
  <PERSON>ont<PERSON>izes,
  Markdown,
  SizedBox,
  Spacing,
} from '@btaskee/design-system';

import { useI18n } from '@hooks';

import { styles } from './styles';

type ProgressItemProps = {
  step?: number;
  title?: string;
  description?: string;
};

const ProgressItem = ({ title, description, step }: ProgressItemProps) => {
  return (
    <BlockView>
      <BlockView
        center
        style={styles.numberStep}
      >
        <CText style={styles.txtNumberStep}>{step}</CText>
      </BlockView>
      <BlockView style={styles.wrapStep}>
        <CText
          bold
          style={styles.txtStep}
        >
          {title}
        </CText>
        <SizedBox
          height={1}
          color={ColorsV2.neutral100}
        />
        <ConditionView
          condition={Boolean(description)}
          viewTrue={
            <BlockView
              flex
              margin={{ bottom: Spacing.SPACE_08 }}
            >
              <Markdown text={description} />
            </BlockView>
          }
        />
      </BlockView>
    </BlockView>
  );
};

export const WorkingDescriptionProgress = () => {
  const { t } = useI18n();

  const DATA = [
    {
      title: t('PROGRESS_TITLE_1'),
      description: t('PROGRESS_DESCRIPTION_1'),
    },
    {
      title: t('PROGRESS_TITLE_2'),
      description: t('PROGRESS_DESCRIPTION_2'),
    },
    {
      title: t('PROGRESS_TITLE_3'),
      description: t('PROGRESS_DESCRIPTION_3'),
    },
  ];

  const header = () => {
    return (
      <CText
        size={FontSizes.SIZE_16}
        padding={{ vertical: Spacing.SPACE_16 }}
      >
        {t('PROGRESS_SUB_TITLE')}
      </CText>
    );
  };

  return (
    <FlatList
      data={DATA}
      showsVerticalScrollIndicator={false}
      keyExtractor={(_, index) => index.toString()}
      renderItem={({ item, index }) => {
        return (
          <ProgressItem
            step={index + 1}
            title={item.title}
            description={item.description}
          />
        );
      }}
      ListHeaderComponent={header}
      contentContainerStyle={styles.container}
    />
  );
};
