import React, { useEffect, useMemo, useRef } from 'react';
import PagerView from 'react-native-pager-view';
import {
  BlockView,
  BottomView,
  HomeMovingProgressPostTaskType,
  NavigationService,
  PrimaryButton,
  RouteName,
  Spacing,
  useUserStore,
} from '@btaskee/design-system';

import { LocationEmpty } from '@components';
import { useI18n, usePostTaskHomeMoving } from '@hooks';

import { AddressDetailHomeMoving } from '../AddressDetailHomeMoving';
import { ChooseAddressMoving } from '../ChooseAddressMoving';
import { styles } from './styles';

export type HomeDetailMovingProps = {
  step: HomeMovingProgressPostTaskType;
};

export const HomeDetailMoving = ({ step }: HomeDetailMovingProps) => {
  const { t } = useI18n();
  const { getDataPostTaskMoving } = usePostTaskHomeMoving();
  const { user } = useUserStore();

  const pagerViewRef = useRef<PagerView>(null);

  const currentPage = useMemo(() => {
    const dataPostTask = getDataPostTaskMoving(step);
    return dataPostTask?.homeDetail?.addressDetail?.address ? 1 : 0;
  }, [getDataPostTaskMoving, step]);

  useEffect(() => {
    pagerViewRef.current?.setPage(currentPage);
  }, [currentPage]);

  if (!user?._id) {
    return (
      <>
        <BlockView flex>
          <BlockView flex>
            <LocationEmpty
              testID="notLoginHomeMovingTxt"
              label={t('LOCATION_EMPTY') + '\n\n' + t('WELCOME_BACK_DES')}
            />
          </BlockView>
          <BottomView margin={{ horizontal: Spacing.SPACE_16 }}>
            <PrimaryButton
              testID="loginNowBtnHomeMoving"
              title={t('SIGN_UP_NOW')}
              onPress={() =>
                NavigationService.navigate(RouteName.Auth, {
                  isGoBack: true,
                })
              }
            />
          </BottomView>
        </BlockView>
      </>
    );
  }

  return (
    <PagerView
      ref={pagerViewRef}
      initialPage={currentPage}
      style={styles.container}
      scrollEnabled={false}
    >
      <ChooseAddressMoving
        step={step}
        isFocused={currentPage === 0}
      />
      <AddressDetailHomeMoving step={step} />
    </PagerView>
  );
};
