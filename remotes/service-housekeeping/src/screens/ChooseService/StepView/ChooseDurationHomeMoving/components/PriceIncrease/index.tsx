import React from 'react';
import Animated, { FadeIn, FadeOut } from 'react-native-reanimated';
import {
  BlockView,
  BorderRadius,
  ColorsV2,
  ConditionView,
  CText,
  Icon,
  Spacing,
} from '@btaskee/design-system';

import { useI18n } from '@hooks';

import { styles } from './styles';

type PriceIncreaseProps = {
  isShow?: boolean;
};

export const PriceIncrease = ({ isShow }: PriceIncreaseProps) => {
  const { t } = useI18n();

  return (
    <ConditionView
      condition={Boolean(isShow)}
      viewTrue={
        <Animated.View
          entering={FadeIn}
          exiting={FadeOut}
        >
          <BlockView
            row
            radius={BorderRadius.RADIUS_08}
            border={{ width: 1, color: ColorsV2.orange500 }}
            style={styles.container}
          >
            <Icon
              size={24}
              name={'icWarning'}
            />
            <BlockView flex>
              <CText margin={{ left: Spacing.SPACE_12 }}>
                {t('SUPPLY_DEMAND_COST_INCREASE')}
              </CText>
            </BlockView>
          </BlockView>
        </Animated.View>
      }
    />
  );
};
