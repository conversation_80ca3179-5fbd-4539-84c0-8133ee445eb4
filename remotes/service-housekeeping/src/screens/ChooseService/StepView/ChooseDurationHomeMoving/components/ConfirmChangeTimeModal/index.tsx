import React, { forwardRef, useImperativeHandle, useRef } from 'react';
import {
  BlockView,
  CModal,
  CText,
  ColorsV2,
  FontSizes,
  PrimaryButton,
  Spacing,
} from '@btaskee/design-system';

import { useI18n } from '@hooks';

export type ConfirmChangeTimeModalHandle = {
  open: (params: {
    oldHomeDetail: {
      isCleaningRequired: boolean;
      date: any;
      timezone: string;
    };
    newHomeDetail: {
      isCleaningRequired: boolean;
      date: any;
      timezone: string;
    };
    onConfirm: () => void;
  }) => void;
  close: () => void;
};

type ConfirmChangeTimeModalProps = {};

export const ConfirmChangeTimeModal = forwardRef<
  ConfirmChangeTimeModalHandle,
  ConfirmChangeTimeModalProps
>((props, ref) => {
  const { t } = useI18n();
  const modalRef = useRef<any>(null);
  const paramsRef = useRef<any>(null);

  useImperativeHandle(ref, () => ({
    open: (params) => {
      paramsRef.current = params;
      modalRef.current?.open();
    },
    close: () => {
      modalRef.current?.close();
    },
  }));

  const handleConfirm = () => {
    paramsRef.current?.onConfirm?.();
    modalRef.current?.close();
  };

  return (
    <CModal
      ref={modalRef}
      title={t('CONFIRM_CHANGE_TIME')}
    >
      <BlockView padding={Spacing.SPACE_16}>
        <CText
          size={FontSizes.SIZE_14}
          color={ColorsV2.neutral600}
          margin={{ bottom: Spacing.SPACE_24 }}
        >
          {t('CONFIRM_CHANGE_TIME_MESSAGE')}
        </CText>
        <PrimaryButton
          title={t('CONFIRM')}
          onPress={handleConfirm}
        />
      </BlockView>
    </CModal>
  );
});
