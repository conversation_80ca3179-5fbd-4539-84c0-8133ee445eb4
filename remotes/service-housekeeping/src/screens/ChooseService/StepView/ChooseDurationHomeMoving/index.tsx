import React, { useEffect, useMemo, useRef } from 'react';
import {
  BlockView,
  CText,
  DatePicker,
  DateTimeHelpers,
  FontSizes,
  getHourFromTime,
  HomeMovingProgressPostTaskType,
  IDate,
  KeyboardAware,
  NotePostTask,
  PostTaskHelpers,
  SERVICES,
  SizedBox,
  TimePicker,
  useSettingsStore,
} from '@btaskee/design-system';
import { usePostTaskStore } from '@store';
import { get } from 'lodash-es';

import { useI18n, usePostTaskHomeMoving } from '@hooks';

import {
  ConfirmChangeTimeModal,
  ConfirmChangeTimeModalHandle,
  PriceIncrease,
} from './components';
import { styles } from './styles';

export type ChooseDurationHomeMovingProps = {
  step: HomeMovingProgressPostTaskType;
};

const SETTING_DEFAULT_TIME = {
  MOVING: 9,
  CLEANING_NEW_HOME: 9,
  MIN_HOUR_MOVING: 24, // giá trị được phép booking sau giờ hiện tại là 24
};

const LIMIT_DATE_HOME_MOVING = 30;

export const ChooseDurationHomeMoving = ({
  step,
}: ChooseDurationHomeMovingProps) => {
  const { t } = useI18n();
  const { settings } = useSettingsStore();
  const { getIsPriceIncrease, getDateCleaningDefault } =
    usePostTaskHomeMoving();

  const {
    service,
    date,
    note,
    isApplyNoteForAllTask,
    oldHomeDetail,
    newHomeDetail,
    timezones,
    setDate,
    setDuration,
    setNote,
    setIsApplyNoteForAllTask,
  } = usePostTaskStore();

  const confirmChangeTimeModalRef = useRef<ConfirmChangeTimeModalHandle>(null);
  const dateMoving =
    date ||
    PostTaskHelpers.getDefaultDateTime({
      serviceName: SERVICES.HOME_MOVING,
    });

  const defaultTaskTime = get(
    service,
    'defaultTaskTime',
    SETTING_DEFAULT_TIME.MOVING,
  );

  const postingLimits = useMemo(() => {
    let from = 0;
    let to = 0;
    const postingLimitsSetting = get(service, 'postingLimits');
    if (postingLimitsSetting) {
      // Extract hour from time string (e.g., "09:00" -> 9)
      from = getHourFromTime(postingLimitsSetting.from);
      to = getHourFromTime(postingLimitsSetting.to);
    }
    return {
      from,
      to,
    };
  }, [service]);

  const minDateMoving = useMemo(() => {
    if (!timezones?.oldHome) {
      return DateTimeHelpers.toDayTz({})
        .add(SETTING_DEFAULT_TIME.MIN_HOUR_MOVING + 1, 'hour')
        .startOf('hour');
    }

    // Sau giờ hiện tại 24h + 1 để hơn giờ hiện tại 1 tiếng và không cần check tới phút
    let minDate = DateTimeHelpers.toDayTz({ timezone: timezones.oldHome })
      .add(SETTING_DEFAULT_TIME.MIN_HOUR_MOVING + 1, 'hour')
      .startOf('hour');

    // Nếu giờ hiện tại lớn hơn postingLimits.to(18h) set qua thêm 1 ngày nữa
    if (minDate.hour() >= postingLimits.to) {
      minDate = DateTimeHelpers.toDateTz({
        timezone: timezones.oldHome,
        date: minDate,
      })
        .add(1, 'day')
        .hour(postingLimits.from)
        .startOf('hour');
    }
    return minDate;
  }, [postingLimits.from, postingLimits.to, timezones?.oldHome]);

  const rangeTimeMoving = useMemo(() => {
    if (!timezones?.oldHome || !dateMoving) {
      return {
        min: minDateMoving,
        max: minDateMoving,
      };
    }

    let min = DateTimeHelpers.toDateTz({
      timezone: timezones.oldHome,
      date: minDateMoving,
    });
    const max = DateTimeHelpers.toDateTz({
      timezone: timezones.oldHome,
      date: dateMoving,
    })
      .hour(postingLimits.to)
      .startOf('hour');

    const isAfter = DateTimeHelpers.checkIsAfter({
      timezone: timezones.oldHome,
      firstDate: dateMoving,
      secondDate: minDateMoving,
      unit: 'day',
    });
    if (isAfter) {
      min = DateTimeHelpers.toDateTz({
        timezone: timezones.oldHome,
        date: dateMoving,
      })
        .hour(postingLimits.from)
        .startOf('minute');
    }

    return {
      min,
      max,
    };
  }, [
    dateMoving,
    minDateMoving,
    postingLimits.from,
    postingLimits.to,
    timezones?.oldHome,
  ]);

  useEffect(() => {
    setDataDefault();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const setDataDefault = () => {
    // Set duration mặc định
    setDuration(1);

    // Nếu chưa có date moving thì mới set date default
    if (!dateMoving && timezones?.oldHome) {
      // date time default là sau ngày 2 hiện tại 24h và 9h sáng
      const defaultDateTime = DateTimeHelpers.toDateTz({
        timezone: timezones.oldHome,
        date: minDateMoving,
      }).toDate();
      if (minDateMoving.hour() <= defaultTaskTime) {
        defaultDateTime.setHours(defaultTaskTime);
      }
      onChangeDateTimeMoving(
        DateTimeHelpers.formatToString({
          timezone: timezones.oldHome,
          date: defaultDateTime,
        }),
      );
    }
  };

  const onChangeDateTimeMoving = async (value: IDate) => {
    if (!timezones?.oldHome || !value) {
      return;
    }

    const momentDateMoving = dateMoving
      ? DateTimeHelpers.toDateTz({
          timezone: timezones.oldHome,
          date: dateMoving,
        })
      : null;

    // Nếu data không thay đổi thì không gọi action
    if (
      momentDateMoving &&
      momentDateMoving.isSame(value, 'year') &&
      momentDateMoving.isSame(value, 'month') &&
      momentDateMoving.isSame(value, 'day') &&
      momentDateMoving.isSame(value, 'hour') &&
      momentDateMoving.isSame(value, 'minute')
    )
      return;

    let datetime = value;
    const isSameOrBefore = DateTimeHelpers.checkIsSameOrBefore({
      timezone: timezones.oldHome,
      firstDate: value,
      secondDate: minDateMoving,
    });
    if (isSameOrBefore) {
      datetime = DateTimeHelpers.toDateTz({
        timezone: timezones.oldHome,
        date: minDateMoving,
      });
    }
    if (
      oldHomeDetail?.isCleaningRequired ||
      newHomeDetail?.isCleaningRequired
    ) {
      const dateClean = getDateCleaningDefault(datetime);
      setTimeout(() => {
        if (!confirmChangeTimeModalRef?.current?.open) {
          return;
        }
        confirmChangeTimeModalRef.current?.open({
          oldHomeDetail: {
            isCleaningRequired: oldHomeDetail?.isCleaningRequired || false,
            date: dateClean?.old,
            timezone: timezones?.oldHome || '',
          },
          newHomeDetail: {
            isCleaningRequired: newHomeDetail?.isCleaningRequired || false,
            date: dateClean?.new,
            timezone: timezones?.newHome || '',
          },
          onConfirm: () => {
            setDate(datetime, service);
            // Handle cleaning dates if needed
          },
        });
      }, 500);
    } else {
      if (timezones?.oldHome) {
        setDate(
          DateTimeHelpers.formatToString({
            timezone: timezones.oldHome,
            date: datetime,
          }),
          service,
        );
      }
    }
  };

  if (step !== HomeMovingProgressPostTaskType.Step4) {
    return null;
  }

  return (
    <BlockView style={styles.container}>
      <BlockView style={styles.content}>
        <KeyboardAware
          keyboardShouldPersistTaps="handled"
          testID="ChooseDurationHomeMovingScrollView"
          contentContainerStyle={styles.containerScroll}
          showsHorizontalScrollIndicator={false}
        >
          <CText
            bold
            size={FontSizes.SIZE_18}
            style={styles.txtLabel}
          >
            {t('LABEL_TIME_MOVING')}
          </CText>
          <BlockView>
            {timezones?.oldHome && (
              <>
                <DatePicker
                  testID="datePickerHomeMoving"
                  value={dateMoving}
                  onChange={onChangeDateTimeMoving}
                  settingSystem={settings?.settingSystem}
                  minDay={DateTimeHelpers.toDateTz({
                    timezone: timezones.oldHome,
                    date: minDateMoving,
                  }).add(-1, 'hour')}
                  limitDate={LIMIT_DATE_HOME_MOVING}
                  noShowTitle
                  timezone={timezones.oldHome}
                />
                <TimePicker
                  testID="timePickerHomeMoving"
                  value={dateMoving}
                  onChange={onChangeDateTimeMoving}
                  settingSystem={settings?.settingSystem}
                  minTime={rangeTimeMoving.min}
                  maxTime={rangeTimeMoving.max}
                  timezone={timezones.oldHome}
                  isDisabledMinimumTime
                />
              </>
            )}
          </BlockView>

          <PriceIncrease isShow={getIsPriceIncrease()?.moving} />

          <SizedBox height={24} />

          <NotePostTask
            testID="notePostTaskHomeMoving"
            title={t('LABEL_NOTE_FOR_TASKER')}
            description={t('TASK_NOTE_DESCRIPTION')}
            placeholder={t('SERVICE_NOTE_CONTENT')}
            setNote={setNote}
            value={note}
            service={service}
            isApplyNoteForAllTask={isApplyNoteForAllTask}
            setNoteForAllTask={setIsApplyNoteForAllTask}
          />
        </KeyboardAware>
      </BlockView>
      <ConfirmChangeTimeModal ref={confirmChangeTimeModalRef} />
    </BlockView>
  );
};
