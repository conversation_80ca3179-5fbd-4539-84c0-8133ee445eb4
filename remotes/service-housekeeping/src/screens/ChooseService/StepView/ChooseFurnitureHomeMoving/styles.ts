import { StyleSheet } from 'react-native';
import { BorderRadius, ColorsV2, Spacing } from '@btaskee/design-system';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorsV2.neutralWhite,
    paddingHorizontal: Spacing.SPACE_16,
  },
  contentContainer: {
    paddingBottom: 200,
  },
  title: {
    color: ColorsV2.neutral800,
    marginTop: Spacing.SPACE_16,
  },
  image: {
    width: '60%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  furnitureItem: {
    marginBottom: Spacing.SPACE_12,
  },
  optionItem: {
    marginBottom: Spacing.SPACE_12,
  },
  oversizeSection: {
    backgroundColor: ColorsV2.yellow50,
    borderRadius: BorderRadius.RADIUS_08,
    padding: Spacing.SPACE_16,
    marginTop: Spacing.SPACE_24,
    borderWidth: 1,
    borderColor: ColorsV2.orange200,
  },
  oversizeIcon: {
    tintColor: ColorsV2.orange500,
  },
});
