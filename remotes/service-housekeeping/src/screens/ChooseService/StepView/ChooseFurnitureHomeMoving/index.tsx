import React, { useMemo } from 'react';
import {
  BlockView,
  CText,
  FastImage,
  FontSizes,
  getTextWithLocale,
  HomeMovingProgressPostTaskType,
  IconAssets,
  IFurnitureItem,
  ProcessButton,
  ScrollView,
  SizedBox,
  Spacing,
  useAppStore,
} from '@btaskee/design-system';
import { usePostTaskStore } from '@store';
import { debounce } from 'lodash-es';

import { useAppNavigation, useI18n, usePostTaskHomeMoving } from '@hooks';
import { imgOverSizeFurniture } from '@images';
import { RouteName } from '@navigation/RouteName';

import { FurnitureItem } from './components/FurnitureItem';
import { OptionFurnitureItem } from './components/OptionFurnitureItem';
import { styles } from './styles';

export type ChooseFurnitureHomeMovingProps = {
  step: HomeMovingProgressPostTaskType;
};

export const ChooseFurnitureHomeMoving = ({
  step,
}: ChooseFurnitureHomeMovingProps) => {
  const navigation = useAppNavigation();
  const { t } = useI18n();
  const { locale } = useAppStore();
  const { getDetailSettingHomeMoving } = usePostTaskHomeMoving();

  const { oldHomeDetail, furniture, currentStep, setFurniture } =
    usePostTaskStore();

  const furnitureSetting = useMemo(() => {
    return getDetailSettingHomeMoving(
      oldHomeDetail?.addressDetail?.taskPlace?.city,
    )?.furniture;
  }, [
    getDetailSettingHomeMoving,
    oldHomeDetail?.addressDetail?.taskPlace?.city,
  ]);

  const onChangeQuantityItem = useMemo(
    () =>
      debounce((item: IFurnitureItem) => {
        const newFurniture = [...(furniture || [])];
        const index = newFurniture.findIndex(
          (furnitureItem) => furnitureItem.name === item.name,
        );
        if (index !== -1) {
          if ((item.quantity || 0) === 0) {
            newFurniture.splice(index, 1);
          } else {
            const newItem = { ...newFurniture[index], ...item };
            // Handle options cleanup when quantity decreases
            if (newItem.options?.length) {
              let totalQuantityOption = 0;
              newItem.options?.forEach((option) => {
                totalQuantityOption +=
                  option.options?.reduce((a, b) => a + (b.quantity || 0), 0) ||
                  0;
              });
              // If quantity changes to less than total option quantity, remove options
              if ((item.quantity || 0) < totalQuantityOption) {
                newItem.options = [];
              }
            }
            newFurniture[index] = newItem;
          }
        } else if ((item.quantity || 0) > 0) {
          const { options, ...newItem } = item as any;
          newFurniture.push(newItem);
        }
        setFurniture(newFurniture);
      }, 300),
    [furniture, setFurniture],
  );

  const onChangeImagesItem = useMemo(
    () =>
      debounce((item: IFurnitureItem) => {
        const newFurniture = [...(furniture || [])];
        const index = newFurniture.findIndex(
          (furnitureItem) => furnitureItem.name === item.name,
        );
        if (index !== -1) {
          newFurniture[index] = { ...newFurniture[index], images: item.images };
          setFurniture(newFurniture);
        }
      }, 1000),
    [furniture, setFurniture],
  );

  const goToMovingProgress = () => {
    // trackingServiceView({
    //   screenName: TrackingScreenNames.ProcessIntroduction,
    //   serviceName: SERVICES.HOME_MOVING,
    //   entryPoint: TrackingScreenNames.FurnitureList,
    // });

    // Navigate to the global route for HomeMovingDescriptionProgress
    // @ts-ignore - This route exists in the main navigation but not in local RouteName
    navigation.navigate(RouteName.StandardPackagingProcess);
  };

  if (currentStep !== step) {
    return null;
  }

  return (
    <BlockView style={styles.container}>
      <ScrollView
        testID="ChooseFurnitureHomeMovingScrollView"
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.contentContainer}
      >
        <CText
          bold
          size={FontSizes.SIZE_18}
          style={styles.title}
        >
          {getTextWithLocale(furnitureSetting?.text, locale)}
        </CText>

        <BlockView
          center
          width={'100%'}
          height={200}
          margin={{ top: Spacing.SPACE_16 }}
        >
          <FastImage
            source={imgOverSizeFurniture}
            resizeMode="contain"
            style={styles.image}
          />
        </BlockView>

        <SizedBox height={Spacing.SPACE_16} />

        {furnitureSetting?.type?.map((e, i) => {
          const furnitureIndex = furniture?.findIndex(
            (el) => el.name === e.name,
          );
          const currentFurniture =
            furniture?.find((el) => el.name === e.name) || {};
          const currentFurnitureQuantity = currentFurniture?.quantity || 0;

          return (
            <FurnitureItem
              testIDs={{
                item: `furnitureItemBtn-${i}`,
                minus: `minusBtnFurnitureItem-${i}`,
                plus: `plusBtnFurnitureItem-${i}`,
                quantity: `quantityTxtFurnitureItem-${i}`,
              }}
              key={e.name}
              title={getTextWithLocale(e.text, locale)}
              subTitle={getTextWithLocale(e.description, locale)}
              quantity={currentFurnitureQuantity}
              images={currentFurniture?.images}
              onChange={(quantity) =>
                onChangeQuantityItem({
                  ...e,
                  quantity,
                  images: currentFurniture?.images,
                })
              }
              onChangeImages={(images) => {
                onChangeImagesItem({
                  ...currentFurniture,
                  ...e,
                  quantity: currentFurnitureQuantity,
                  images: images || [],
                });
              }}
              otherContent={() => {
                if (e.options?.length) {
                  return e.options.map((el, index) => {
                    const currentOption = currentFurniture?.options?.find(
                      (element) => element.name === el.name,
                    );
                    const otherOptions = currentFurniture?.options?.filter(
                      (element) => element.name !== el.name,
                    );

                    const currentQuantity =
                      currentOption?.options?.reduce(
                        (a, b) => a + (b?.quantity || 0),
                        0,
                      ) || 0;

                    let otherQuantity = 0;
                    otherOptions?.forEach((otherOption) => {
                      otherQuantity +=
                        otherOption?.options?.reduce(
                          (a, b) => a + (b?.quantity || 0),
                          0,
                        ) || 0;
                    });

                    return (
                      <OptionFurnitureItem
                        testIDs={{
                          item: `optionBtnFurnitureItem-${i}-${index}`,
                          value: `quantityTxtFurnitureItem-${i}-${index}`,
                        }}
                        key={el.name}
                        sourceIcon={IconAssets.icSetting}
                        label={getTextWithLocale(el.text, locale)}
                        subLabel={getTextWithLocale(el.description, locale)}
                        value={currentQuantity ? `x${currentQuantity}` : false}
                        items={el.options}
                        itemAsLabel={(item: any) =>
                          getTextWithLocale(item.text, locale)
                        }
                        itemAsSelected={(item: any) =>
                          !!currentOption?.options?.find(
                            (element: any) => element.name === item.name,
                          )?.quantity
                        }
                        itemAsQuantity={(item: any) => {
                          const currentOptionQuantity =
                            currentOption?.options?.find(
                              (option: any) => option.name === item.name,
                            );
                          return currentOptionQuantity?.quantity || 0;
                        }}
                        itemAsMax={(item) => {
                          if (currentFurniture?.quantity) {
                            const otherItems = currentFurniture?.options
                              ?.find((element) => element.name === el.name)
                              ?.options?.filter(
                                (subOption) => subOption.name !== item.name,
                              );

                            const otherItemQuantity =
                              otherItems?.reduce(
                                (a, b) => a + (b?.quantity || 0),
                                0,
                              ) || 0;

                            const max =
                              currentFurniture?.quantity -
                              otherQuantity -
                              otherItemQuantity;

                            return max;
                          }
                        }}
                        onChangeQuantityItem={(
                          item: any,
                          newQuantity: number,
                        ) => {
                          const newSubOption = {
                            ...item,
                            quantity: newQuantity,
                          };
                          const newFurniture = [...(furniture || [])];
                          const currentFurnitureIndex = newFurniture.findIndex(
                            (furnitureItem) => furnitureItem.name === e.name,
                          );

                          if (currentFurnitureIndex !== -1) {
                            const currentListOptions =
                              newFurniture[currentFurnitureIndex].options || [];
                            const currentOptionIndex =
                              currentListOptions?.findIndex(
                                (element: any) => element.name === el.name,
                              );
                            const subOptionIndex = currentListOptions[
                              currentOptionIndex
                            ]?.options?.findIndex(
                              (subOption: any) => subOption.name === item.name,
                            );

                            if (newQuantity) {
                              if (currentOptionIndex === -1) {
                                newFurniture[currentFurnitureIndex].options = [
                                  ...currentListOptions,
                                  {
                                    ...el,
                                    options: [newSubOption],
                                  },
                                ];
                              } else {
                                if (subOptionIndex === -1) {
                                  newFurniture[currentFurnitureIndex].options?.[
                                    currentOptionIndex
                                  ].options?.push(newSubOption);
                                } else {
                                  currentListOptions?.[
                                    currentOptionIndex
                                  ]?.options?.splice(
                                    subOptionIndex as number,
                                    1,
                                    newSubOption,
                                  );
                                }
                              }
                            } else {
                              currentListOptions?.[
                                currentOptionIndex
                              ]?.options?.splice(subOptionIndex as number, 1);
                              if (
                                !currentListOptions?.[currentOptionIndex]
                                  ?.options?.length
                              ) {
                                newFurniture[
                                  currentFurnitureIndex
                                ].options?.splice(
                                  currentOptionIndex as number,
                                  1,
                                );
                              }
                            }
                            setFurniture(newFurniture);
                          }
                        }}
                      />
                    );
                  });
                }
                return null;
              }}
            />
          );
        })}

        <BlockView style={styles.oversizeSection}>
          <CText>{t('WARNING_OVER_SIZE_FURNITURE')}</CText>
        </BlockView>

        <SizedBox height={30} />
        <ProcessButton
          label={t('MOVING_PROCESS')}
          onPress={goToMovingProgress}
        />
      </ScrollView>
    </BlockView>
  );
};
