import React, { ReactNode, useMemo, useRef, useState } from 'react';
import { FlatList, ViewStyle } from 'react-native';
import {
  BlockView,
  ColorsV2,
  ConditionView,
  CText,
  FontSizes,
  IconAssets,
  IconImage,
  IImage,
  SizedBox,
  Spacing,
  TouchableOpacity,
  useUploadImage,
} from '@btaskee/design-system';

import {
  ChangeQuantityItem,
  UploadImageModal,
  UploadImageModalHandle,
} from '@components';
import { useI18n } from '@hooks';

import { ImageFurniture } from '../ImageFurniture';
import { UploadImageButton } from '../UploadImageButton';
import { styles } from './styles';

type FurniturePropsItem = {
  testIDs?: {
    item?: string;
    quantity?: string;
    minus?: string;
    plus?: string;
  };
  title: string;
  subTitle?: string;
  quantity?: number;
  images?: string[];
  otherContent?: () => ReactNode;
  onChange?: (currentQuantity: number) => void;
  onChangeImages?: (images?: string[]) => void;
};

const MIN = 0;
const MAX = 99;
const STEP = 1;
const MAX_IMAGES = 5;

export const FurnitureItem = ({
  testIDs,
  title,
  subTitle,
  quantity,
  images,
  otherContent,
  onChange,
  onChangeImages,
}: FurniturePropsItem) => {
  const { t } = useI18n();

  const { getMultipleLinkImages, showToastMaxImages } = useUploadImage();
  const uploadImageModalRef = useRef<UploadImageModalHandle>(null);
  const [currentQuantity, setCurrentQuantity] = useState(quantity || MIN);
  const [currentImages, setCurrentImages] =
    useState<FurniturePropsItem['images']>(images);

  const isActive = Boolean(currentQuantity);
  const lengthCurrentImages = currentImages?.length || 0;

  const styleContainer: ViewStyle = useMemo(() => {
    if (isActive) {
      return {
        borderColor: ColorsV2.orange500,
      };
    }
    return {
      borderColor: ColorsV2.neutral100,
      backgroundColor: ColorsV2.neutralWhite,
    };
  }, [isActive]);

  const onChangeQuantityItem = (newValue: number) => {
    setCurrentQuantity(newValue);
    onChange?.(newValue);
    if (newValue <= MIN) {
      setCurrentImages([]);
      onChangeImages?.([]);
    }
  };

  const _onUploadImage = () => {
    if (lengthCurrentImages < MAX_IMAGES) {
      uploadImageModalRef.current?.open();
    } else {
      showToastMaxImages();
    }
  };

  /**
   * Handles successful image upload from modal
   * Purpose: Processes uploaded images and updates component state
   * @param imageUrls - Array of image URIs from upload modal
   * @returns {void} No return value, updates component state
   */
  const onUploadImageSuccess = async (imageUrls?: string[]) => {
    if (!imageUrls || imageUrls.length === 0) return;

    try {
      // Convert image URIs to IImage objects for processing
      const imageObjects: IImage[] = imageUrls.map((uri, index) => ({
        uri,
        name: `furniture_image_${Date.now()}_${index}`,
        type: 'image/png',
      }));

      // Process images through upload hook
      const processedImages = await getMultipleLinkImages({
        images: imageObjects,
        endPath: 'furniture',
        keyPrefix: 'home_moving',
      });

      // Extract URIs from processed images
      const newImageUrls = processedImages.map((img) => img.link || img.uri);

      // Combine with existing images, respecting max limit
      const combinedImages = [...(currentImages || []), ...newImageUrls];
      const limitedImages = combinedImages.slice(0, MAX_IMAGES);

      setCurrentImages(limitedImages);
      onChangeImages?.(limitedImages);

      // Images processed successfully
    } catch (error) {
      console.error('Error processing uploaded images:', error);
    }
  };

  const onRemoveImage = (index: number) => () => {
    const newImages = [...(currentImages || [])];
    newImages.splice(index, 1);
    setCurrentImages(newImages);
    onChangeImages?.(newImages);
  };

  return (
    <TouchableOpacity
      testID={testIDs?.item}
      activeOpacity={0.8}
      style={[styles.container, styleContainer]}
      disabled={isActive}
      onPress={() => onChangeQuantityItem(1)}
    >
      <BlockView
        row
        center
      >
        <CText
          flex
          bold
          size={FontSizes.SIZE_16}
          color={isActive ? ColorsV2.orange500 : ColorsV2.neutral800}
        >
          {title}
        </CText>
        <ConditionView
          condition={isActive}
          viewTrue={
            <IconImage
              source={IconAssets.icTick}
              color={ColorsV2.green500}
            />
          }
          viewFalse={
            <IconImage
              source={IconAssets.icArrowDown}
              size={20}
              color={ColorsV2.neutral400}
            />
          }
        />
      </BlockView>
      {subTitle && (
        <CText
          color={ColorsV2.neutral600}
          numberOfLines={1}
          margin={{ top: Spacing.SPACE_08 }}
        >
          {subTitle}
        </CText>
      )}

      <ConditionView
        condition={isActive}
        viewTrue={
          <BlockView margin={{ top: Spacing.SPACE_16 }}>
            <ChangeQuantityItem
              testIDs={{
                value: testIDs?.quantity,
                minus: testIDs?.minus,
                plus: testIDs?.plus,
              }}
              title={t('QUANTITY')}
              value={currentQuantity}
              min={MIN}
              max={MAX}
              step={STEP}
              onChange={(newValue) => onChangeQuantityItem(newValue)}
            />
            <SizedBox height={Spacing.SPACE_16} />
            <ConditionView
              condition={Boolean(lengthCurrentImages)}
              viewTrue={
                <BlockView row>
                  <FlatList
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    data={currentImages}
                    keyExtractor={(item) => item}
                    renderItem={({ item, index }) => {
                      return (
                        <ImageFurniture
                          source={{ uri: item }}
                          onRemove={onRemoveImage(index)}
                        />
                      );
                    }}
                  />
                  <ConditionView
                    condition={lengthCurrentImages < MAX_IMAGES}
                    viewTrue={
                      <TouchableOpacity
                        activeOpacity={0.8}
                        style={styles.circleBtn}
                        onPress={_onUploadImage}
                      >
                        <IconImage
                          source={IconAssets.icPlusFill}
                          color={ColorsV2.neutral400}
                          size={50}
                        />
                      </TouchableOpacity>
                    }
                  />
                </BlockView>
              }
              viewFalse={
                <UploadImageButton
                  label={t('ACTUAL_PHOTOGRAPHY')}
                  onPress={_onUploadImage}
                />
              }
            />
            <ConditionView
              condition={Boolean(otherContent?.())}
              viewTrue={
                <BlockView margin={{ top: Spacing.SPACE_16 }}>
                  {otherContent?.()}
                </BlockView>
              }
            />
          </BlockView>
        }
      />
      <UploadImageModal
        ref={uploadImageModalRef}
        maxFiles={MAX_IMAGES - lengthCurrentImages}
        onUploadImageSuccess={onUploadImageSuccess}
      />
    </TouchableOpacity>
  );
};
