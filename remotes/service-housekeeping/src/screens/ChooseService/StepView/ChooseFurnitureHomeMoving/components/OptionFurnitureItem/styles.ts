import { StyleSheet } from 'react-native';
import { BorderRadius, ColorsV2, Spacing } from '@btaskee/design-system';

export const styles = StyleSheet.create({
  container: {
    borderWidth: 1,
    borderColor: ColorsV2.neutral100,
    borderRadius: BorderRadius.RADIUS_08,
    padding: Spacing.SPACE_12,
    marginBottom: Spacing.SPACE_12,
  },
  textValueContainer: {
    backgroundColor: ColorsV2.orange50,
    borderRadius: BorderRadius.RADIUS_04,
    paddingHorizontal: Spacing.SPACE_08,
    paddingVertical: Spacing.SPACE_04,
  },
  expandedContent: {
    borderTopWidth: 1,
    borderTopColor: ColorsV2.neutral100,
  },
  itemContainer: {
    borderWidth: 1,
    borderColor: ColorsV2.neutral100,
    borderRadius: BorderRadius.RADIUS_08,
    padding: Spacing.SPACE_12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: ColorsV2.neutral10,
  },
});
