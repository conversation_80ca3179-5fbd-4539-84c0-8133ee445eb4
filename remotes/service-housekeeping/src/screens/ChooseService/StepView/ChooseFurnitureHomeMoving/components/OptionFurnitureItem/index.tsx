import React, { useMemo, useState } from 'react';
import { ImageProps, TouchableOpacityProps } from 'react-native';
import Feather from 'react-native-vector-icons/Feather';
import {
  BlockView,
  ColorsV2,
  ConditionView,
  CText,
  FontSizes,
  HitSlop,
  IconAssets,
  IconImage,
  Spacing,
  TouchableOpacity,
} from '@btaskee/design-system';

import { ChangeQuantityItem } from '@components';

import { styles } from './styles';

export type OptionFurnitureItemProps<T = any> = {
  testIDs?: {
    item?: string;
    value?: string;
  };
  label: string;
  subLabel?: string;
  value?: boolean | string;
  sourceIcon: ImageProps['source'];
  items?: T[];
  itemAsLabel?: (item: T) => string;
  itemAsQuantity?: (item: T) => number;
  itemAsMax?: (item: T) => number | undefined;
  onChangeQuantityItem?: (item: T, newQuantity: number) => void;
  itemAsSelected?: (option: T) => boolean;
};

type ItemProps = {
  label?: string;
  isActive?: boolean;
  margin?: any;
  value?: number;
  min?: number;
  max?: number;
  onChange?: (newValue: number) => void;
};

const Item = ({ label, isActive, margin, ...props }: ItemProps) => {
  const activeStyle: TouchableOpacityProps['style'] = useMemo(() => {
    if (isActive) {
      return {
        backgroundColor: ColorsV2.orange50,
        borderColor: ColorsV2.orange500,
        borderWidth: 1,
      };
    }
  }, [isActive]);

  return (
    <BlockView
      margin={margin}
      style={[styles.itemContainer, activeStyle]}
    >
      <CText
        flex
        bold
        size={FontSizes.SIZE_14}
      >
        {label}
      </CText>
      <ChangeQuantityItem {...props} />
    </BlockView>
  );
};

export const OptionFurnitureItem = <T,>({
  testIDs,
  items,
  label,
  value,
  sourceIcon,
  subLabel,
  itemAsLabel,
  itemAsSelected,
  itemAsMax,
  itemAsQuantity,
  onChangeQuantityItem,
}: OptionFurnitureItemProps<T>) => {
  const [isCollapsible, setIsCollapsible] = useState(true);

  const onPress = () => {
    toggleCollapsible();
  };

  const toggleCollapsible = () => {
    setIsCollapsible(!isCollapsible);
  };

  const activeStyle: TouchableOpacityProps['style'] = useMemo(() => {
    const isActive = Boolean(value) && isCollapsible;

    if (isActive) {
      return {
        backgroundColor: ColorsV2.orange50,
        borderColor: ColorsV2.orange500,
      };
    }
  }, [value, isCollapsible]);

  return (
    <BlockView style={[styles.container, activeStyle]}>
      <TouchableOpacity
        testID={testIDs?.item}
        hitSlop={HitSlop.MEDIUM}
        activeOpacity={0.7}
        onPress={onPress}
      >
        <BlockView
          row
          center
        >
          <IconImage
            source={sourceIcon}
            size={24}
          />
          <BlockView
            row
            flex
            padding={{ horizontal: Spacing.SPACE_12 }}
          >
            <CText
              margin={{ right: Spacing.SPACE_08 }}
              size={FontSizes.SIZE_14}
            >
              {label}
            </CText>
          </BlockView>
          <ConditionView
            condition={Boolean(value) && isCollapsible}
            viewTrue={
              <BlockView style={styles.textValueContainer}>
                <CText
                  testID={testIDs?.value}
                  color={ColorsV2.orange500}
                  size={FontSizes.SIZE_14}
                >
                  {value}
                </CText>
              </BlockView>
            }
            viewFalse={
              <IconImage
                source={
                  isCollapsible ? IconAssets.icArrowUp : IconAssets.icArrowDown
                }
                size={20}
                color={ColorsV2.neutral400}
              />
            }
          />
        </BlockView>
      </TouchableOpacity>
      <ConditionView
        condition={!isCollapsible}
        viewTrue={
          <BlockView
            style={styles.expandedContent}
            margin={{ top: Spacing.SPACE_12 }}
            padding={{ top: Spacing.SPACE_12 }}
          >
            <ConditionView
              condition={Boolean(subLabel)}
              viewTrue={
                <CText
                  margin={{ bottom: Spacing.SPACE_12 }}
                  size={FontSizes.SIZE_14}
                >
                  {subLabel}
                </CText>
              }
            />
            <BlockView>
              {items?.map((item, index) => {
                const isLastItem = index === items.length - 1;

                return (
                  <Item
                    key={index.toString()}
                    label={itemAsLabel?.(item)}
                    isActive={itemAsSelected?.(item)}
                    margin={{ bottom: isLastItem ? 0 : Spacing.SPACE_16 }}
                    max={itemAsMax?.(item)}
                    min={0}
                    value={itemAsQuantity?.(item)}
                    onChange={(newQuantity) =>
                      onChangeQuantityItem?.(item, newQuantity)
                    }
                  />
                );
              })}
            </BlockView>
          </BlockView>
        }
      />
    </BlockView>
  );
};
