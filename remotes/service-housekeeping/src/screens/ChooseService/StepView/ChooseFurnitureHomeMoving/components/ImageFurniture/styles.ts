import { StyleSheet } from 'react-native';
import { ColorsV2, DeviceHelper, Spacing } from '@btaskee/design-system';

export const styles = StyleSheet.create({
  container: {
    paddingTop: Spacing.SPACE_12,
    paddingRight: Spacing.SPACE_16,
  },
  imageContainer: {
    borderRadius: 8,
    backgroundColor: ColorsV2.neutral50,
    overflow: 'hidden',
    width: <PERSON><PERSON><PERSON><PERSON><PERSON>.WINDOW.WIDTH / 6,
    height: DeviceHelper.WINDOW.WIDTH / 6,
  },
  image: {
    width: '100%',
    height: '100%',
  },
  removeBtn: {
    position: 'absolute',
    top: 4,
    right: 8,
    zIndex: 99,
  },
});
