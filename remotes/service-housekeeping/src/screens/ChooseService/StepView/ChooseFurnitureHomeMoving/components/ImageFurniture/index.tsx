import React from 'react';
import {
  BlockView,
  FastImage,
  FastImageComponentProps,
  HitSlop,
  IconAssets,
  IconImage,
  TouchableOpacity,
} from '@btaskee/design-system';

import { styles } from './styles';

type ImageFurnitureProps = {
  source?: FastImageComponentProps['source'];
  onRemove?: () => void;
};

export const ImageFurniture = ({ source, onRemove }: ImageFurnitureProps) => {
  return (
    <BlockView style={styles.container}>
      <TouchableOpacity
        style={styles.removeBtn}
        hitSlop={HitSlop.MEDIUM}
        onPress={onRemove}
      >
        <IconImage source={IconAssets.icRemoveCircle} />
      </TouchableOpacity>
      <BlockView style={styles.imageContainer}>
        <FastImage
          source={source}
          style={styles.image}
          resizeMode="contain"
        />
      </BlockView>
    </BlockView>
  );
};
