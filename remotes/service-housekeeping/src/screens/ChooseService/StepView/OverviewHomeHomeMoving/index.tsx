import React, { ReactNode } from 'react';
import {
  BlockView,
  ColorsV2,
  ConditionView,
  CText,
  DateWithGMT,
  FontSizes,
  getTextWithLocale,
  HitSlop,
  HomeMovingProgressPostTaskType,
  IconAssets,
  IconImage,
  RowInfo,
  ScrollView,
  Spacing,
  TouchableOpacity,
  TypeFormatDate,
  useAppStore,
} from '@btaskee/design-system';
import { usePostTaskStore } from '@store';

import { HomeDetailInfo, Section } from '@components';
import { useI18n, usePostTaskHomeMoving } from '@hooks';

import { styles } from './styles';

export type OverviewHomeHomeMovingProps = {
  step: HomeMovingProgressPostTaskType;
};

type BlockProps = {
  children: ReactNode;
  title: string;
  onEdit?: () => void;
};

const Block = ({ children, title, onEdit }: BlockProps) => {
  return (
    <Section
      title={title}
      leftTitle={
        <ConditionView
          condition={Boolean(onEdit)}
          viewTrue={
            <TouchableOpacity
              activeOpacity={0.7}
              hitSlop={HitSlop.MEDIUM}
              onPress={onEdit}
            >
              <IconImage
                source={IconAssets.icChange}
                color={ColorsV2.green500}
                size={20}
              />
            </TouchableOpacity>
          }
        />
      }
    >
      <BlockView style={styles.blockContainer}>{children}</BlockView>
    </Section>
  );
};

export const OverviewHomeHomeMoving = ({
  step,
}: OverviewHomeHomeMovingProps) => {
  const { t } = useI18n();
  const { locale } = useAppStore();
  const { onPressStep } = usePostTaskHomeMoving();

  const {
    oldHomeDetail,
    newHomeDetail,
    furniture,
    currentStep,
    timezones,
    date,
    note,
  } = usePostTaskStore();

  const onEdit = (stepEdit: HomeMovingProgressPostTaskType) => {
    onPressStep(stepEdit);
  };

  if (currentStep !== step) {
    return null;
  }

  return (
    <ScrollView
      testID="OverviewHomeMovingScrollView"
      showsVerticalScrollIndicator={false}
      style={styles.container}
      contentContainerStyle={styles.contentContainerStyle}
    >
      <Block title={t('ADDRESS')}>
        <HomeDetailInfo
          testIDs={{
            shortAddress: 'shortAddressTxtOldHomeDetail',
            homeType: 'homeTypeTxtOldHomeDetail',
          }}
          title={t('PLACE_TO_MOVE')}
          fontSizeTitle={FontSizes.SIZE_16}
          homeDetail={oldHomeDetail}
          onEdit={() => {
            onEdit(HomeMovingProgressPostTaskType.Step1);
            // trackingServiceClick({
            //   screenName: TrackingScreenNames.AdditionalServices,
            //   serviceName: SERVICES.HOME_MOVING,
            //   action: TRACKING_ACTION.EditCurrentLocation,
            // });
          }}
          isShowShortAddress
          isShowBorderBottom
        />
        <HomeDetailInfo
          testIDs={{
            shortAddress: 'shortAddressTxtNewHomeDetail',
            homeType: 'homeTypeTxtNewHomeDetail',
          }}
          title={t('PLACE_TO_MOVE_TO')}
          fontSizeTitle={FontSizes.SIZE_16}
          homeDetail={newHomeDetail}
          onEdit={() => {
            onEdit(HomeMovingProgressPostTaskType.Step2);
            // trackingServiceClick({
            //   screenName: TrackingScreenNames.OverviewHomeMoving,
            //   serviceName: SERVICES.HOME_MOVING,
            //   action: TRACKING_ACTION.EditCurrentLocation,
            // });
          }}
          isShowShortAddress
        />
      </Block>
      <ConditionView
        condition={Boolean(furniture?.length)}
        viewTrue={
          <Block
            title={t('LABEL_OVER_FURNITURE')}
            onEdit={() => {
              onEdit(HomeMovingProgressPostTaskType.Step3);
              // trackingServiceClick({
              //   screenName: TrackingScreenNames.OverviewHomeMoving,
              //   serviceName: SERVICES.HOME_MOVING,
              //   action: TRACKING_ACTION.EditOversizedFurniture,
              // });
            }}
          >
            {furniture?.map((e, i) => {
              const isFirstItem = i === 0;
              const quantityTxt = e.quantity ? `x${e.quantity}` : '';
              return (
                <BlockView
                  key={i.toString()}
                  row
                  padding={{ vertical: Spacing.SPACE_12 }}
                  border={{
                    top: {
                      width: isFirstItem ? 0 : 1,
                      color: ColorsV2.neutral100,
                    },
                  }}
                >
                  <CText flex>{getTextWithLocale(e.text, locale)}</CText>
                  <CText
                    bold
                    color={ColorsV2.orange500}
                  >
                    {quantityTxt}
                  </CText>
                </BlockView>
              );
            })}
          </Block>
        }
      />
      <Block
        title={t('WORK_TIME')}
        onEdit={() => {
          onEdit(HomeMovingProgressPostTaskType.Step4);
          // trackingServiceClick({
          //   screenName: TrackingScreenNames.OverviewHomeMoving,
          //   serviceName: SERVICES.HOME_MOVING,
          //   action: TRACKING_ACTION.EditWorkingTime,
          // });
        }}
      >
        <RowInfo
          label={t('TIME_MOVING')}
          value={
            <DateWithGMT
              timezone={timezones.oldHome}
              date={date}
              typeFormat={TypeFormatDate.HourMinuteDate}
            />
          }
          styleInfo={{ paddingTop: Spacing.SPACE_04 }}
        />
        <ConditionView
          condition={Boolean(oldHomeDetail?.isCleaningRequired)}
          viewTrue={
            <RowInfo
              border={{ top: { width: 1, color: ColorsV2.neutral100 } }}
              label={t('PLACE_TO_MOVE_CLEANING')}
              value={
                <DateWithGMT
                  timezone={timezones.oldHome}
                  date={oldHomeDetail?.date}
                  typeFormat={TypeFormatDate.HourMinuteDate}
                />
              }
              styleInfo={{ paddingTop: Spacing.SPACE_04 }}
            />
          }
        />
        <ConditionView
          condition={Boolean(newHomeDetail?.isCleaningRequired)}
          viewTrue={
            <RowInfo
              border={{ top: { width: 1, color: ColorsV2.neutral100 } }}
              label={t('PLACE_TO_MOVE_TO_CLEANING')}
              value={
                <DateWithGMT
                  timezone={timezones.newHome}
                  date={newHomeDetail?.date}
                  typeFormat={TypeFormatDate.HourMinuteDate}
                />
              }
              styleInfo={{ paddingTop: Spacing.SPACE_04 }}
            />
          }
        />
      </Block>
      <ConditionView
        condition={Boolean(note)}
        viewTrue={
          <BlockView>
            <BlockView>
              <CText
                bold
                size={FontSizes.SIZE_18}
              >
                {t('NOTES')}
              </CText>
            </BlockView>
            <BlockView
              style={styles.blockContainer}
              padding={{ vertical: Spacing.SPACE_16 }}
            >
              <CText>{note}</CText>
            </BlockView>
          </BlockView>
        }
      />
    </ScrollView>
  );
};
