import React, { useEffect, useMemo, useRef } from 'react';
import {
  BlockView,
  BottomView,
  ColorsV2,
  FlatList,
  HomeMovingProgressPostTaskType,
  IconAssets,
  IconImage,
  IUserLocation,
  LocationItem,
  PrimaryButton,
  SizedBox,
  Spacing,
} from '@btaskee/design-system';
import { usePostTaskStore } from '@store';
import { debounce } from 'lodash-es';

import { AddIsInBuildingButton, LocationEmpty, Section } from '@components';
import { useAppNavigation, useI18n, usePostTaskHomeMoving } from '@hooks';

import { styles } from './styles';

export type ChooseAddressMovingProps = {
  step: HomeMovingProgressPostTaskType;
  isFocused?: boolean;
};

export const ChooseAddressMoving = ({
  step,
  isFocused,
}: ChooseAddressMovingProps) => {
  const navigation = useAppNavigation();
  const { t } = useI18n();
  const {
    getContentByStep,
    onChangeLocation,
    getDataPostTaskMoving,
    getBlackListLocation,
    onAddLocation,
    getLocations,
    onAddIsInBuilding,
    getIsShowAddIsInBuilding,
  } = usePostTaskHomeMoving();

  const { currentStep } = usePostTaskStore();

  const dataPostTask = useMemo(() => {
    return getDataPostTaskMoving(step);
  }, [getDataPostTaskMoving, step]);

  const currentHomeDetail = useMemo(() => {
    return dataPostTask?.homeDetail;
  }, [dataPostTask?.homeDetail]);

  const title = useMemo(() => {
    return getContentByStep(step).title;
  }, [getContentByStep, step]);

  const locations = useMemo(() => {
    const blackList = getBlackListLocation();
    return getLocations(blackList?.address);
  }, [getBlackListLocation, getLocations]);

  const isShowAddIsInBuilding = useMemo(() => {
    return getIsShowAddIsInBuilding();
  }, [getIsShowAddIsInBuilding]);

  // const onAddNewAddressDebounce = useRef(debounce(onAddLocation, 500)).current;

  // useEffect(() => {
  //   if (
  //     !locations?.length &&
  //     currentStep === step &&
  //     !currentHomeDetail?.addressDetail?.address
  //   ) {
  //     onAddNewAddressDebounce({ title, step, isShowAddIsInBuilding });
  //   }
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [currentStep, step, isShowAddIsInBuilding]);

  const onAddNewAddress = () => {
    onAddLocation({ title, step, isShowAddIsInBuilding });
  };

  const onEditAddress = (item: IUserLocation) => () => {
    // Navigate to the global EditLocation route
    // @ts-ignore - This route exists in the main navigation but not in local RouteName
    navigation.navigate('EditLocation', { location: item });
  };

  const renderItem = ({ item }: { item: IUserLocation }) => {
    return (
      <LocationItem
        testID={`addressItem-${item?.lat}-${item?.lng}`}
        shortAddress={item?.shortAddress}
        address={item?.address}
        onPress={() => onChangeLocation?.({ step, location: item })}
        isShowUpdate={Boolean(item?._id)}
        onPressUpdate={onEditAddress(item)}
      />
    );
  };

  const keyExtractor = (item: IUserLocation) => `${item?.lat}-${item?.lng}`;

  const itemSeparatorComponent = () => <SizedBox height={16} />;

  const listHeaderComponent = () => {
    if (isShowAddIsInBuilding) {
      return (
        <BlockView margin={{ bottom: Spacing.SPACE_16 }}>
          <AddIsInBuildingButton
            testID="addIsInBuildingButton"
            onPress={onAddIsInBuilding}
          />
        </BlockView>
      );
    }
    return null;
  };

  const listEmptyComponent = () => (
    <LocationEmpty
      testID="locationEmptyChooseAddress"
      label={t('LOCATION_EMPTY')}
    />
  );

  if (!isFocused) {
    return null;
  }

  return (
    <BlockView flex>
      <BlockView style={styles.container}>
        <SizedBox height={Spacing.SPACE_08} />
        <Section
          title={t('CHOOSE_ADDRESS')}
          testID="addressSectionTitle"
        >
          <SizedBox height={16} />
          <FlatList
            testID="scrollChooseAddress"
            data={locations || []}
            keyExtractor={keyExtractor}
            renderItem={renderItem}
            showsVerticalScrollIndicator={false}
            ItemSeparatorComponent={itemSeparatorComponent}
            contentContainerStyle={styles.contentContainer}
            ListEmptyComponent={listEmptyComponent}
            ListHeaderComponent={listHeaderComponent}
          />
        </Section>
      </BlockView>
      <BottomView margin={{ horizontal: Spacing.SPACE_16 }}>
        <PrimaryButton
          testID="addNewAddressBtnHomeMoving"
          title={t('ADD_ADDRESS')}
          titleProps={{ bold: false, margin: { left: Spacing.SPACE_08 } }}
          left={
            <IconImage
              source={IconAssets.icPlus}
              color={ColorsV2.neutralWhite}
            />
          }
          onPress={onAddNewAddress}
        />
      </BottomView>
    </BlockView>
  );
};
