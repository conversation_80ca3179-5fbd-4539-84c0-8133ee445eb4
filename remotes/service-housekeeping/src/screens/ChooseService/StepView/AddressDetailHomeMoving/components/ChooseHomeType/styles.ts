import { StyleSheet } from 'react-native';
import { BorderRadius, ColorsV2, Spacing } from '@btaskee/design-system';

export const styles = StyleSheet.create({
  homeTypeItem: {
    padding: Spacing.SPACE_16,
    borderRadius: BorderRadius.RADIUS_08,
    borderWidth: 1,
    borderColor: ColorsV2.neutral100,
    backgroundColor: ColorsV2.neutralWhite,
    marginBottom: Spacing.SPACE_12,
  },
  homeTypeItemSelected: {
    borderColor: ColorsV2.orange500,
    backgroundColor: ColorsV2.orange50,
  },
  imageContainer: {
    width: '80%',
    height: 150,
    borderRadius: BorderRadius.RADIUS_08,
    backgroundColor: ColorsV2.neutral50,
    justifyContent: 'center',
    alignItems: 'center',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  itemContainer: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: Spacing.SPACE_12,
    paddingBottom: Spacing.SPACE_16,
    paddingTop: Spacing.SPACE_12,
    alignItems: 'center',
  },
});
