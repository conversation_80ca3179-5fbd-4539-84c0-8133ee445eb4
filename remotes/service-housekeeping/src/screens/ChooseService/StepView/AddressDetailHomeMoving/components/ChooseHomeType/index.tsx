import React, { useMemo } from 'react';
import { ViewStyle } from 'react-native';
import {
  BlockView,
  ColorsV2,
  CText,
  FastImage,
  FastImageComponentProps,
  FontSizes,
  getTextWithLocale,
  HomeMovingProgressPostTaskType,
  Spacing,
  TouchableOpacity,
  TouchableOpacityProps,
} from '@btaskee/design-system';
import { usePostTaskStore } from '@store';

import { Section } from '@components';
import { useI18n, usePostTaskHomeMoving } from '@hooks';

import { styles } from './styles';

type ItemProps = {
  title?: string;
  isActive?: boolean;
  style?: ViewStyle;
  sourceImage?: FastImageComponentProps['source'];
  onPress?: TouchableOpacityProps['onPress'];
};

const Item = ({ sourceImage, title, isActive, style, onPress }: ItemProps) => {
  const styleContainer: ViewStyle = useMemo(() => {
    return {
      backgroundColor: isActive ? ColorsV2.orange50 : ColorsV2.neutral50,
      borderColor: isActive ? ColorsV2.orange500 : ColorsV2.neutral100,
    };
  }, [isActive]);

  return (
    <TouchableOpacity
      style={[styles.itemContainer, styleContainer, style]}
      onPress={onPress}
    >
      <BlockView style={styles.imageContainer}>
        <FastImage
          source={sourceImage}
          style={styles.image}
        />
      </BlockView>
      <CText
        size={FontSizes.SIZE_16}
        center
        margin={{ top: Spacing.SPACE_12 }}
      >
        {title}
      </CText>
    </TouchableOpacity>
  );
};

type ChooseHomeTypeProps = {
  step: HomeMovingProgressPostTaskType;
};

export const ChooseHomeType = ({ step }: ChooseHomeTypeProps) => {
  const { t } = useI18n();
  const {
    getDetailSettingHomeMoving,
    getDataPostTaskMoving,
    onChooseHomeType,
    getImageHomeType,
  } = usePostTaskHomeMoving();

  const { currentStep } = usePostTaskStore();

  const dataPostTask = useMemo(() => {
    return getDataPostTaskMoving(step);
  }, [getDataPostTaskMoving, step]);

  const currentHomeDetail = useMemo(() => {
    return dataPostTask.homeDetail;
  }, [dataPostTask.homeDetail]);

  const currentCity = useMemo(() => {
    return currentHomeDetail?.addressDetail?.taskPlace?.city;
  }, [currentHomeDetail?.addressDetail?.taskPlace?.city]);

  const homeTypesSetting = useMemo(() => {
    return getDetailSettingHomeMoving(currentCity)?.homeType || [];
  }, [getDetailSettingHomeMoving, currentCity]);

  const isFocusStep = useMemo(() => {
    return currentStep === step;
  }, [currentStep, step]);

  if (!isFocusStep || !currentHomeDetail?.addressDetail?.address) {
    return null;
  }

  return (
    <Section
      title={t('CHOOSE_HOME_TYPE')}
      margin={{ top: Spacing.SPACE_24 }}
    >
      <BlockView
        row
        margin={{ top: Spacing.SPACE_16 }}
      >
        {homeTypesSetting?.map((homeType, index) => {
          const isSelected =
            currentHomeDetail?.homeType?.name === homeType?.name;
          const marginRight = index ? 0 : Spacing.SPACE_16;

          return (
            <Item
              key={homeType.name}
              title={getTextWithLocale(homeType.text)}
              sourceImage={getImageHomeType(homeType.name)}
              isActive={isSelected}
              style={{ marginRight }}
              onPress={() => onChooseHomeType({ step, homeType })}
            />
          );
        })}
      </BlockView>
    </Section>
  );
};
