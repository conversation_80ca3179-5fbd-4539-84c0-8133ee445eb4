import React, { useMemo } from 'react';
import { TouchableOpacityProps } from 'react-native';
import {
  BlockView,
  ColorsV2,
  ConditionView,
  CText,
  FontSizes,
  Spacing,
  TouchableOpacity,
} from '@btaskee/design-system';

import { styles } from './styles';

type AreaItemProps = {
  isSelected?: boolean;
  label?: string;
  subLabel?: string;
  description?: string;
} & Pick<TouchableOpacityProps, 'onPress' | 'style'>;

export const AreaItem = ({
  label,
  subLabel,
  description,
  isSelected,
  style,
  onPress,
}: AreaItemProps) => {
  const styleSelected = useMemo(() => {
    if (isSelected) {
      return {
        backgroundColor: ColorsV2.orange50,
        borderColor: ColorsV2.orange500,
      };
    }
    return {};
  }, [isSelected]);

  return (
    <TouchableOpacity
      style={[styles.container, styleSelected, style]}
      onPress={onPress}
    >
      <BlockView
        flex
        justify="center"
      >
        <CText
          color={isSelected ? ColorsV2.orange500 : ColorsV2.neutral800}
          size={FontSizes.SIZE_14}
          bold
        >
          {label}
        </CText>
        <ConditionView
          condition={Boolean(subLabel)}
          viewTrue={
            <CText
              size={FontSizes.SIZE_12}
              flex
              margin={{ top: Spacing.SPACE_04 }}
              color={isSelected ? ColorsV2.orange500 : ColorsV2.neutral800}
            >
              {subLabel}
            </CText>
          }
        />
      </BlockView>
      <CText
        color={isSelected ? ColorsV2.green500 : ColorsV2.neutral800}
        size={FontSizes.SIZE_14}
      >
        {description}
      </CText>
    </TouchableOpacity>
  );
};
