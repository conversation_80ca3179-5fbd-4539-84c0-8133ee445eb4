import React, { useEffect, useMemo, useState } from 'react';
import {
  AnimationHel<PERSON>,
  BlockView,
  ColorsV2,
  ConditionView,
  <PERSON><PERSON>t,
  <PERSON><PERSON><PERSON><PERSON>per,
  getTextWithLocale,
  HomeMovingProgressPostTaskType,
  IHomeDetail,
  IOptionHomeDetail,
  NameHomeTypeHomeMoving,
  NameOptionByHomeTypeHomeMoving,
  Spacing,
  TypeHomeTypeHomeMoving,
  useAppStore,
} from '@btaskee/design-system';
import { HOME_MOVING_CONFIG } from '@constant';
import { usePostTaskStore } from '@store';

import { OptionItem, Section } from '@components';
import { useI18n, usePostTaskHomeMoving } from '@hooks';

import { AreaItem } from '../AreaItem';
import { ChangeHomeTypeButton } from '../ChangeHomeTypeButton';

type ChooseOptionAddressProps = {
  step: HomeMovingProgressPostTaskType;
};

const WIDTH_ITEM = DeviceHelper.WINDOW.WIDTH / 2 - Spacing.SPACE_24;
const WIDTH_BYROAD_DEFAULT = 2; // HOME_MOVING_CONFIG.widthByroad.min equivalent

export const ChooseOptionAddress = ({ step }: ChooseOptionAddressProps) => {
  const { t } = useI18n();
  const { locale } = useAppStore();
  const {
    getDetailSettingHomeMoving,
    getDetailSettingHomeType,
    getDataPostTaskMoving,
    getIconOptionHomeType,
    onChooseHomeType,
    getContentByHomeType,
    getContentOptionHomeType,
  } = usePostTaskHomeMoving();

  const { isInBuilding, setHomeDetail } = usePostTaskStore();

  const [widthTemptByroad, setWidthTemptByroad] =
    useState(WIDTH_BYROAD_DEFAULT);

  const isStep2 = useMemo(() => {
    return step === HomeMovingProgressPostTaskType.Step2;
  }, [step]);

  const dataPostTask = useMemo(() => {
    return getDataPostTaskMoving(step);
  }, [getDataPostTaskMoving, step]);

  const currentHomeDetail = useMemo(() => {
    return dataPostTask.homeDetail;
  }, [dataPostTask.homeDetail]);

  const currentCity = useMemo(() => {
    return currentHomeDetail?.addressDetail?.taskPlace?.city;
  }, [currentHomeDetail?.addressDetail?.taskPlace?.city]);

  const nameHomeTypeMoving = useMemo(() => {
    return currentHomeDetail?.homeType?.name;
  }, [currentHomeDetail?.homeType?.name]);

  const detailSettingHomeMoving = useMemo(() => {
    return getDetailSettingHomeMoving(currentCity);
  }, [currentCity, getDetailSettingHomeMoving]);

  const detailHomeType = useMemo(() => {
    return getDetailSettingHomeType({
      city: currentCity,
      nameHomeType: nameHomeTypeMoving,
    });
  }, [currentCity, getDetailSettingHomeType, nameHomeTypeMoving]);

  const contentByHomeType = useMemo(() => {
    return getContentByHomeType(nameHomeTypeMoving);
  }, [getContentByHomeType, nameHomeTypeMoving]);

  const listDetailOptionsHomeType = useMemo(() => {
    return detailSettingHomeMoving?.options;
  }, [detailSettingHomeMoving?.options]);

  const widthChangeHomeTypeButton = useMemo(() => {
    return (detailHomeType?.type?.length || 0) % 2 === 0 ? '100%' : WIDTH_ITEM;
  }, [detailHomeType?.type?.length]);

  const titleOptions = useMemo(() => {
    let title = t('OPTION');

    switch (detailHomeType?.name) {
      case NameHomeTypeHomeMoving.house:
        title = t('OPTION_HOUSE');
        break;

      case NameHomeTypeHomeMoving.apartment:
        title = t('OPTION_APARTMENT');
        break;

      default:
        break;
    }

    return title;
  }, [detailHomeType?.name, t]);

  const isStepCurrentLocation = useMemo(() => {
    return step === HomeMovingProgressPostTaskType.Step1;
  }, [step]);

  const isStepNewLocation = useMemo(() => {
    return step === HomeMovingProgressPostTaskType.Step2;
  }, [step]);

  const detailHomeTypeOptions = useMemo(() => {
    // If at step 2 (new location) and isInBuilding, only show stairs transport option
    let result = detailHomeType?.options;
    if (isStepNewLocation && isInBuilding) {
      result = result?.filter(
        (e) => e === NameOptionByHomeTypeHomeMoving.stairsTransport,
      );
    }
    // If at step 1 (current location) and isInBuilding, don't show stairs transport option
    if (isStepCurrentLocation && isInBuilding) {
      result = result?.filter(
        (e) => e !== NameOptionByHomeTypeHomeMoving.stairsTransport,
      );
    }

    return result;
  }, [
    detailHomeType?.options,
    isInBuilding,
    isStepCurrentLocation,
    isStepNewLocation,
  ]);

  useEffect(() => {
    // Reset default width for byroad when home type changes
    setWidthTemptByroad(WIDTH_BYROAD_DEFAULT);
  }, [nameHomeTypeMoving]);

  const onSelectArea = (type: TypeHomeTypeHomeMoving) => {
    const newHomeType: IHomeDetail['homeType'] = {
      name: detailHomeType?.name,
      text: detailHomeType?.text,
      type: {
        name: type.name,
        text: type.text,
        oldHomeCleaning: type.oldHomeCleaning,
        newHomeCleaning: type.newHomeCleaning,
      },
    };

    AnimationHelpers.runLayoutAnimation();
    setHomeDetail({
      step,
      homeDetail: {
        ...currentHomeDetail,
        homeType: newHomeType,
      },
    });
  };

  const onChangeHomeTypeMoving = () => {
    const newHomeType = detailSettingHomeMoving?.homeType?.find((e) => {
      const reverseHomeType =
        nameHomeTypeMoving === NameHomeTypeHomeMoving.house
          ? NameHomeTypeHomeMoving.apartment
          : NameHomeTypeHomeMoving.house;
      return e.name === reverseHomeType;
    });

    if (!newHomeType) return;

    // TODO: Add tracking if needed
    // trackingServiceClick({
    //   screenName:
    //     step === HomeMovingProgressPostTaskType.Step1
    //       ? TrackingScreenNames.CurrentLocation
    //       : TrackingScreenNames.NewLocation,
    //   serviceName: SERVICES.HOME_MOVING,
    //   action: TRACKING_ACTION.ChangeHouseType,
    //   additionalInfo: {
    //     newHomeType: newHomeType?.name,
    //     oldHomeType: nameHomeTypeMoving,
    //   },
    // });

    onChooseHomeType({
      step,
      homeType: newHomeType,
    });
  };

  const onChangeValueOptionHomeType = ({
    value,
    option,
  }: {
    value?: boolean;
    option?: any; // TODO: Add proper typing
  }) => {
    const options = [...(currentHomeDetail?.options || [])];
    const optionIndex = options.findIndex((el) => el.name === option?.name);

    if (!value) {
      options.splice(optionIndex, 1);
    } else {
      options.push({
        name: option?.name,
        text: option?.text,
      });
    }

    setHomeDetail({
      step,
      homeDetail: {
        ...currentHomeDetail,
        options,
      },
    });
  };

  return (
    <BlockView>
      <ConditionView
        condition={Boolean(detailHomeType?.type?.length) && !isStep2}
        viewTrue={
          <BlockView margin={{ top: Spacing.SPACE_16 }}>
            <Section title={contentByHomeType.labelArea}>
              <BlockView
                row
                wrap
                justify="space-between"
              >
                {detailHomeType?.type?.map((type) => {
                  const isSelected =
                    currentHomeDetail?.homeType?.type?.name === type.name;
                  return (
                    <AreaItem
                      key={type?.name}
                      label={getTextWithLocale(type?.text, locale)}
                      isSelected={isSelected}
                      subLabel={
                        type.description &&
                        getTextWithLocale(type.description, locale)
                      }
                      onPress={() => onSelectArea(type)}
                      style={{
                        marginTop: Spacing.SPACE_16,
                        width: WIDTH_ITEM,
                      }}
                    />
                  );
                })}
                <ChangeHomeTypeButton
                  width={widthChangeHomeTypeButton}
                  paddingTop={Spacing.SPACE_24}
                  onPress={onChangeHomeTypeMoving}
                />
              </BlockView>
            </Section>
          </BlockView>
        }
      />
      <ConditionView
        condition={
          Boolean(nameHomeTypeMoving === NameHomeTypeHomeMoving.house) &&
          !isStep2
        }
        viewTrue={
          <BlockView
            backgroundColor={ColorsV2.orange50}
            border={{ width: 1, color: ColorsV2.orange500 }}
            padding={Spacing.SPACE_16}
            radius={8}
            margin={{ top: Spacing.SPACE_24, bottom: Spacing.SPACE_16 }}
          >
            <CText
              testID="warningHomeTypeHouseTxt"
              color={ColorsV2.neutral800}
            >
              {t('WARNING_HOME_TYPE_HOUSE')}
            </CText>
          </BlockView>
        }
      />

      <ConditionView
        condition={Boolean(nameHomeTypeMoving)}
        viewTrue={
          <BlockView style={{ marginTop: Spacing.SPACE_24 }}>
            <Section title={titleOptions}>
              {detailHomeTypeOptions?.map((nameOption) => {
                const option = listDetailOptionsHomeType?.find(
                  (e) => e.name === nameOption,
                );
                const icon = getIconOptionHomeType(nameOption);
                const currentOption = currentHomeDetail?.options?.find(
                  (e) => e.name === nameOption,
                );
                const options = [...(currentHomeDetail?.options || [])];
                const optionIndex = options.findIndex(
                  (el) => el.name === option?.name,
                );
                const matchOption = options[optionIndex] || {};
                const isExist = optionIndex !== -1;
                const contentOption = getContentOptionHomeType(nameOption);

                const value = currentOption?.option?.text
                  ? getTextWithLocale(currentOption?.option?.text, locale)
                  : Boolean(currentOption);

                return (
                  <React.Fragment key={nameOption}>
                    <OptionItem
                      label={getTextWithLocale(option?.text, locale)}
                      sourceIcon={icon}
                      value={value}
                      isExpand={Boolean(isStepNewLocation && isInBuilding)}
                      titleOptions={contentOption.title}
                      noteOptions={contentOption.note}
                      options={option?.options}
                      optionAsLabel={(item) =>
                        getTextWithLocale(item?.text, locale)
                      }
                      optionAsSelected={(item) =>
                        item?.name === currentOption?.option?.name
                      }
                      onValueChange={(_) =>
                        onChangeValueOptionHomeType({ value: _, option })
                      }
                      changeQuantityItemProps={
                        nameOption === NameOptionByHomeTypeHomeMoving.byroad
                          ? {
                              title: t('CHOOSE_WIDTH_BYROAD'),
                              // titleTextTransform: 'none',
                              min: HOME_MOVING_CONFIG.widthByroad.min,
                              max: HOME_MOVING_CONFIG.widthByroad.max,
                              step: HOME_MOVING_CONFIG.widthByroad.step,
                              value: matchOption.width || widthTemptByroad,
                              unit: t('METER'),
                              onChange: (width) => {
                                const newOption: IOptionHomeDetail = {
                                  name: option?.name,
                                  text: option?.text,
                                  ...matchOption,
                                  width,
                                };
                                if (isExist) {
                                  options[optionIndex] = newOption;
                                  setHomeDetail({
                                    step,
                                    homeDetail: {
                                      ...currentHomeDetail,
                                      options,
                                    },
                                  });
                                } else {
                                  setWidthTemptByroad(width);
                                }
                              },
                            }
                          : null
                      }
                      optionAsOnPress={(item) => {
                        const isExistItem =
                          options[optionIndex]?.option?.name === item.name;
                        if (!isExistItem) {
                          const newOption: IOptionHomeDetail = {
                            name: option?.name,
                            text: option?.text,
                            option: {
                              name: item.name,
                              text: item.text,
                            },
                          };
                          if (
                            nameOption === NameOptionByHomeTypeHomeMoving.byroad
                          ) {
                            newOption.width =
                              options[optionIndex]?.width || widthTemptByroad;
                          }
                          if (isExist) {
                            options[optionIndex] = newOption;
                          } else if (item) {
                            options.push(newOption);
                          }
                        } else {
                          if (isExist) {
                            options.splice(optionIndex, 1);
                          }
                        }
                        setHomeDetail({
                          step,
                          homeDetail: {
                            ...currentHomeDetail,
                            options,
                          },
                        });
                      }}
                    />
                  </React.Fragment>
                );
              })}
            </Section>
            <ConditionView
              condition={isStep2 && !isInBuilding}
              viewTrue={
                <BlockView margin={{ top: Spacing.SPACE_16 }}>
                  <ChangeHomeTypeButton onPress={onChangeHomeTypeMoving} />
                </BlockView>
              }
            />
          </BlockView>
        }
      />
    </BlockView>
  );
};
