import React from 'react';
import { ViewStyle } from 'react-native';
import {
  ColorsV2,
  CText,
  FontSizes,
  TouchableOpacity,
} from '@btaskee/design-system';

import { useI18n } from '@hooks';

import { styles } from './styles';

type ChangeHomeTypeButtonProps = {
  onPress: () => void;
  width?: ViewStyle['width'];
  paddingTop?: ViewStyle['paddingTop'];
};

export const ChangeHomeTypeButton = ({
  width,
  paddingTop,
  onPress,
}: ChangeHomeTypeButtonProps) => {
  const { t } = useI18n();

  return (
    <TouchableOpacity
      testID="changeHomeTypeMovingBtn"
      activeOpacity={0.8}
      style={[styles.editButton, { width, paddingTop }]}
      onPress={onPress}
    >
      <CText
        color={ColorsV2.green500}
        size={FontSizes.SIZE_14}
        right
      >
        {t('CHANGE_HOME_TYPE')}
      </CText>
    </TouchableOpacity>
  );
};
