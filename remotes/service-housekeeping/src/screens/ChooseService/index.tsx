import React, {
  useCallback,
  useEffect,
  useLayoutEffect,
  useMemo,
  useRef,
} from 'react';
import { BackHandler } from 'react-native';
import PagerView from 'react-native-pager-view';
import {
  BlockView,
  ColorsV2,
  ConditionView,
  HousekeepingProgressPostTaskType,
  IconAssets,
  IconImage,
  PostTaskHelpers,
  PriceButton,
  SERVICES,
  TouchableOpacity,
} from '@btaskee/design-system';
import { useIsFocused } from '@react-navigation/native';
import { usePostTaskStore } from '@store';

import { ProgressMoving } from '@components';
import { useAppNavigation, usePostTask, usePostTaskHousekeeping } from '@hooks';
import { RouteName } from '@navigation/RouteName';

import {
  ChooseDurationHousekeeping,
  ChooseFurnitureHousekeeping,
  HousekeepingDetailMoving,
  OverviewHousekeeping,
} from './StepView';
import { styles } from './styles';

export const ChooseService = () => {
  const navigation = useAppNavigation();
  const isFocused = useIsFocused();

  const {
    service,
    currentStep,
    oldHomeDetail,
    newHomeDetail,
    furniture,
    date,
    promotion,
    setCurrentStep,
    setPrice,
    setDateTime,
    price,
  } = usePostTaskStore();

  const { getContentByStep, onNextStep } = usePostTaskHousekeeping();
  const { getPrice } = usePostTask();
  // const { trackingStepHomeMoving, trackingHomeMovingProgress, getNameOfStep } = useTrackingHomeMoving();

  // const service = useAppSelector(PostTaskSelector.service);
  // const currentStep = useAppSelector(PostTaskHomeMovingSelector.currentStep);
  // const oldHomeDetail = useAppSelector(
  //   PostTaskHomeMovingSelector.oldHomeDetail,
  // );
  // const newHomeDetail = useAppSelector(
  //   PostTaskHomeMovingSelector.newHomeDetail,
  // );
  // const furniture = useAppSelector(PostTaskHomeMovingSelector.furniture);
  // const dateMoving = useAppSelector(PostTaskSelector.date);
  // const promotion = useAppSelector(PostTaskSelector.promotion);
  // const entryPoint = useAppSelector(PostTaskSelector.entryPoint);

  //Biến tempt cache current step để fix lỗi price button đặt trong useMemo
  const currentStepTempt = useRef(currentStep);

  const oldHomeDetailStr = JSON.stringify(oldHomeDetail);
  const newHomeDetailStr = JSON.stringify(newHomeDetail);
  const furnitureStr = JSON.stringify(furniture);
  const dateMovingStr = JSON.stringify(date);
  const promotionStr = JSON.stringify(promotion || {});

  const pagerViewRef = useRef<PagerView>(null);

  const contentByStep = useMemo(() => {
    return getContentByStep(currentStep);
  }, [currentStep, getContentByStep]);

  // const trackingBackAction = useCallback(
  //   (step: HomeMovingProgressPostTaskType) => {
  //     trackingStepHomeMoving({
  //       step,
  //       action: TRACKING_ACTION.Back,
  //     });
  //     trackingHomeMovingProgress(step);
  //   },
  //   [trackingStepHomeMoving, trackingHomeMovingProgress],
  // );

  const onGoBack = useCallback(() => {
    if (currentStep > HomeMovingProgressPostTaskType.Step1) {
      // trackingBackAction(currentStep);
      setCurrentStep(currentStep - 1);
    } else {
      navigation.goBack();
    }
  }, [currentStep, navigation, setCurrentStep]);

  const backAction = useCallback(() => {
    if (currentStep > HomeMovingProgressPostTaskType.Step1) {
      // trackingBackAction(currentStep);
      setCurrentStep(currentStep - 1);
      return true;
    }
    return false;
  }, [currentStep, setCurrentStep]);

  const headerLeft = useMemo(() => {
    return (
      <TouchableOpacity
        testID="header-back"
        onPress={onGoBack}
      >
        <IconImage source={IconAssets.icBack} />
      </TouchableOpacity>
    );
  }, [onGoBack]);

  const renderHeaderRight = useMemo(() => {
    return (
      <TouchableOpacity
        testID="headerInfoButton"
        onPress={() => {
          navigation.navigate(RouteName.IntroService);
        }}
      >
        <IconImage
          source={IconAssets.icMoreInformation}
          size={24}
          color={ColorsV2.neutral900}
        />
      </TouchableOpacity>
    );
  }, [navigation]);

  useLayoutEffect(() => {
    navigation.setOptions({
      title: contentByStep?.title || '',
      headerLeft: () => headerLeft,
      headerRight: () => renderHeaderRight,
    });
  }, [
    contentByStep?.title,
    currentStep,
    headerLeft,
    navigation,
    renderHeaderRight,
  ]);

  // useEffect(() => {
  //   trackingServiceView({
  //     screenName: TrackingScreenNames.CurrentLocation,
  //     serviceName: SERVICES.HOME_MOVING,
  //     entryPoint: entryPoint,
  //   });
  // }, []);

  useEffect(() => {
    if (!date) {
      const defaultDate = PostTaskHelpers.getDefaultDateTime({
        serviceName: SERVICES.HOME_MOVING,
      });
      setDateTime(defaultDate);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [date]);

  useEffect(() => {
    pagerViewRef.current?.setPage(currentStep - 1);
    currentStepTempt.current = currentStep;
  }, [currentStep]);

  useEffect(() => {
    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction,
    );
    if (!isFocused) {
      backHandler.remove();
    }
    return () => backHandler.remove();
  }, [backAction, isFocused]);

  useEffect(() => {
    getPricing();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    oldHomeDetailStr,
    newHomeDetailStr,
    furnitureStr,
    dateMovingStr,
    promotionStr,
  ]);

  const getPricing = () => {
    if (oldHomeDetail?.homeType?.type?.name && newHomeDetail?.homeType?.name) {
      getPrice();
      return;
    }
    setPrice(null);
  };

  const onNext = () => {
    onNextStep(currentStepTempt.current);
  };

  return (
    <>
      <BlockView style={styles.container}>
        <ProgressMoving />
        <PagerView
          ref={pagerViewRef}
          initialPage={currentStep - 1}
          scrollEnabled={false}
          style={styles.pagerView}
        >
          <HomeDetailMoving step={HomeMovingProgressPostTaskType.Step1} />
          <HomeDetailMoving step={HomeMovingProgressPostTaskType.Step2} />
          <ChooseFurnitureHomeMoving
            step={HomeMovingProgressPostTaskType.Step3}
          />
          <ChooseDurationHomeMoving
            step={HomeMovingProgressPostTaskType.Step4}
          />
          <OverviewHomeHomeMoving step={HomeMovingProgressPostTaskType.Step5} />
        </PagerView>
        <ConditionView
          condition={currentStep !== HomeMovingProgressPostTaskType.Step1}
          viewTrue={
            <PriceButton
              testID="btnNextMoving"
              fromScreen={service?.name || ''}
              onPress={onNext}
              pricePostTask={price}
            />
          }
        />
      </BlockView>
    </>
  );
};
