import { StyleSheet } from 'react-native';
import {
  BorderRadius,
  ColorsV2,
  <PERSON><PERSON><PERSON><PERSON>per,
  <PERSON>ontSizes,
  Shadows,
  Spacing,
} from '@btaskee/design-system';

const { WIDTH } = DeviceHelper.WINDOW;

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorsV2.neutralWhite,
  },
  wrap_image: {
    borderRadius: BorderRadius.RADIUS_08,
    overflow: 'hidden',
  },
  txt_serviceName: {
    color: ColorsV2.orange500,
    marginBottom: Spacing.SPACE_16,
  },
  wrap_note: {
    paddingRight: Spacing.SPACE_16,
  },
  titleBtn: {
    fontSize: FontSizes.SIZE_16,
  },
  wrap_bottom: {
    ...Shadows.SHADOW_1,
    paddingBottom: Spacing.SPACE_12,
    paddingHorizontal: Spacing.SPACE_12,
  },
  boxText: {
    paddingLeft: Spacing.SPACE_16,
  },
  content: {
    marginTop: -150,
    paddingHorizontal: Spacing.SPACE_16,
    flex: 1,
  },
  contentContainerStyle: {
    paddingVertical: Spacing.SPACE_24,
  },
  headerBackgroundStyle: {
    width: WIDTH,
    height: 230,
  },
  iconStyle: {
    marginTop: 5,
  },
  headerBackgroundIntroStyle: {
    width: WIDTH - 2 * Spacing.SPACE_16,
    height: WIDTH / 2 - Spacing.SPACE_16,
  },
  txtStyle: {
    lineHeight: 22,
    marginBottom: Spacing.SPACE_16,
  },
});
