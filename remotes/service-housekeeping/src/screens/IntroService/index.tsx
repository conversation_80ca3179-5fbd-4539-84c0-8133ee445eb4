/**
 * <AUTHOR> Quoc Hai
 * @email [<EMAIL>]
 * @create date 2023-11-15 10:18:03
 * @modify date 2023-11-15 10:18:03
 * @desc [description]
 */

import React from 'react';
import {
  bgHeader,
  BlockView,
  BottomView,
  ColorsV2,
  CText,
  FastImage,
  FontSizes,
  IconImage,
  IconImageProps,
  Markdown,
  PrimaryButton,
  ScrollView,
  Spacing,
} from '@btaskee/design-system';
import { usePostTaskStore } from '@store';

import { useAppNavigation, useI18n } from '@hooks';
import {
  icIntroMoving1,
  icIntroMoving2,
  icIntroMoving3,
  icIntroMoving4,
  icIntroMoving5,
  imgIntroMoving,
} from '@images';
import { RouteName } from '@navigation/RouteName';

import { styles } from './styles';

type TextItemProps = {
  icon?: IconImageProps['source'];
  text?: string;
};

const TextItem = ({ icon, text }: TextItemProps) => {
  return (
    <BlockView row>
      <IconImage
        source={icon}
        size={32}
        style={styles.iconStyle}
      />
      <BlockView
        flex
        style={styles.boxText}
      >
        <Markdown
          text={text}
          paragraphStyle={{ color: ColorsV2.neutral500 }}
        />
      </BlockView>
    </BlockView>
  );
};

export const IntroService = () => {
  const navigation = useAppNavigation();
  const { t } = useI18n();
  const { isFirstOpen, setIsFirstOpen } = usePostTaskStore();

  // React.useEffect(() => {
  //   trackingServiceView({
  //     screenName: TrackingScreenNames.ServiceIntroduction,
  //     serviceName: SERVICES.HOME_MOVING,
  //     entryPoint: entryPointRoute || entryPoint,
  //   });
  // }, [entryPointRoute, entryPoint]);

  const onSubmit = async () => {
    if (isFirstOpen) {
      setIsFirstOpen();
      navigation.replace(RouteName.ChooseService);
    } else {
      navigation.goBack();
    }
  };

  return (
    <BlockView style={styles.container}>
      <BlockView flex>
        <FastImage
          resizeMode="cover"
          source={bgHeader}
          style={styles.headerBackgroundStyle}
        />
        <BlockView style={styles.content}>
          <BlockView style={styles.wrap_image}>
            <FastImage
              resizeMode="cover"
              source={imgIntroMoving}
              style={styles.headerBackgroundIntroStyle}
            />
          </BlockView>
          <ScrollView
            style={styles.wrap_note}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.contentContainerStyle}
          >
            <CText
              bold
              size={FontSizes.SIZE_20}
              style={styles.txt_serviceName}
            >
              {t('INTRO_TITLE')}
            </CText>
            <CText style={styles.txtStyle}>{t('SUB_INTRO')}</CText>
            <TextItem
              icon={icIntroMoving1}
              text={t('SUB_INTRO_1')}
            />
            <TextItem
              icon={icIntroMoving2}
              text={t('SUB_INTRO_2')}
            />
            <TextItem
              icon={icIntroMoving3}
              text={t('SUB_INTRO_3')}
            />
            <TextItem
              icon={icIntroMoving4}
              text={t('SUB_INTRO_4')}
            />
            <TextItem
              icon={icIntroMoving5}
              text={t('SUB_INTRO_5')}
            />
          </ScrollView>
        </BlockView>
      </BlockView>
      <BottomView margin={{ horizontal: Spacing.SPACE_16 }}>
        <PrimaryButton
          title={isFirstOpen ? t('INTRO_START_EXPERIENCE') : t('BTN_BACK')}
          onPress={onSubmit}
        />
      </BottomView>
    </BlockView>
  );
};
