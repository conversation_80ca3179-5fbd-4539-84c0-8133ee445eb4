# Home Moving Service E2E Technical Documentation

## Sequential Flow Architecture

### Overview
The Home Moving Service follows a strict 6-step sequential booking flow that cannot be bypassed. Each step must be completed before proceeding to the next, ensuring data integrity and user experience consistency.

### Flow Sequence
```
Step 1: Old Home Address Detail → Step 2: New Home Address Detail → 
Step 3: Furniture Selection → Step 4: DateTime Selection → 
Step 5: Overview → Final: Payment/Confirmation
```

### Step-by-Step Technical Implementation

#### Step 1: Old Home Address Detail Configuration
**Screen**: `HomeDetailMoving` with `HomeMovingProgressPostTaskType.Step1`
**Container**: `PagerView` page 0
**Key Components**:
- `ChooseAddressMoving` - Address selection list
- `AddressDetailHomeMoving` - Address configuration form

**TestID Mapping**:
```javascript
// Address Selection
'scrollChooseAddress' - Main address list container
'addressItem-{lat}-{lng}' - Individual address items
'addNewAddressBtnHomeMoving' - Add new address button
'addIsInBuildingButton' - Same building option

// Address Detail Configuration
'AddressDetailHomeMovingScrollView' - Main scroll container
'homeTypeSelector' - Home type selection
'acreageSelector' - Area size selection
'transportStairsButton' - Transport method selection
'btnNextMoving' - Navigation to next step
```

**Validation Rules**:
- Address must be selected or created
- Home type must be specified
- Area size must be selected
- Transport options are optional but recommended

#### Step 2: New Home Address Detail Configuration
**Screen**: `HomeDetailMoving` with `HomeMovingProgressPostTaskType.Step2`
**Container**: `PagerView` page 1
**Key Components**: Same as Step 1 but for destination address

**TestID Mapping**: Same as Step 1 (reused components)

**Validation Rules**:
- New home address must be different from old home
- Home type and area configuration required
- Transport options available

#### Step 3: Furniture Selection
**Screen**: `ChooseFurnitureHomeMoving`
**Container**: `PagerView` page 2

**TestID Mapping**:
```javascript
'ChooseFurnitureHomeMovingScrollView' - Main container
'furnitureItemBtn-{index}' - Furniture item selector
'plusBtnFurnitureItem-{index}' - Increase quantity
'minusBtnFurnitureItem-{index}' - Decrease quantity
'quantityTxtFurnitureItem-{index}' - Quantity display
'optionBtnFurnitureItem-{index}-{optionIndex}' - Furniture options
```

**Business Logic**:
- Furniture selection is optional
- Quantity can be 0 to unlimited (within reason)
- Some furniture items have sub-options
- Images can be attached to furniture items

#### Step 4: DateTime Selection
**Screen**: `ChooseDurationHomeMoving`
**Container**: `PagerView` page 3

**TestID Mapping**:
```javascript
'ChooseDurationHomeMovingScrollView' - Main container
'datePickerHomeMoving' - Date selection component
'timePickerHomeMoving' - Time selection component
'notePostTaskHomeMoving' - Optional notes field
```

**Validation Rules**:
- Date must be today or future
- Time must respect service availability
- Minimum advance booking time enforced
- Timezone considerations for different locations

#### Step 5: Overview and Confirmation
**Screen**: `OverviewHomeHomeMoving`
**Container**: `PagerView` page 4

**TestID Mapping**:
```javascript
'OverviewHomeMovingScrollView' - Main container
'shortAddressTxtOldHomeDetail' - Old home address display
'shortAddressTxtNewHomeDetail' - New home address display
'homeTypeTxtOldHomeDetail' - Old home type display
'homeTypeTxtNewHomeDetail' - New home type display
'furnitureOverviewSection' - Furniture summary (if any)
'dateTimeOverviewSection' - DateTime summary
```

**Edit Functionality**:
- Each section has edit buttons that navigate back to respective steps
- Data persistence maintained during navigation
- Price recalculation on data changes

#### Final Step: Payment and Confirmation
**Screen**: `ConfirmAndPayment`
**Navigation**: Separate route from main flow

**TestID Mapping**:
```javascript
'scrollViewStep4' - Main payment container
'locationConfirmSection' - Address confirmation
'taskDetailSection' - Service details
'paymentMethodSelector' - Payment method selection
'checkboxPolicyPostTaskHomeMoving' - Terms acceptance
'btnSubmitPostTask' - Final submission button
```

## Test Implementation Patterns

### 1. Sequential Flow Enforcement
```javascript
// Always complete previous steps before testing current step
await completeAddressSteps(); // Steps 1 & 2
await completeFurnitureStep(); // Step 3
await completeDateTimeStep(); // Step 4
// Now test Step 5 functionality
```

### 2. Scroll-to-Reveal Pattern
```javascript
// Use incremental scrolling to avoid overshooting
await element(by.id('scrollContainer')).scroll(150, 'down');
await sleep(500); // Allow UI to settle
await element(by.id('scrollContainer')).scroll(150, 'down');
await expectElementVisible('targetElement');
```

### 3. Element Waiting Strategy
```javascript
// Use appropriate timeouts based on operation type
await waitForElement('simpleElement', 3000); // UI elements
await waitForElement('networkElement', 8000); // Network operations
await waitForElement('complexElement', 10000); // Complex calculations
```

### 4. Error Handling Pattern
```javascript
try {
  await tapId('optionalElement');
} catch (error) {
  // Element might not be available in all scenarios
  console.log('Optional element not found, continuing...');
}
```

## Performance Optimization Strategies

### 1. Helper Function Reusability
- `completeAddressSteps()` - Completes both address configuration steps
- `completeFurnitureStep()` - Handles furniture selection
- `completeDateTimeStep()` - Manages date/time selection
- `completeFullFlow()` - Executes entire flow for final tests

### 2. Efficient Scrolling
- Use 150-200px increments instead of large scrolls
- Add small delays (500ms) between scroll actions
- Verify element visibility after scrolling

### 3. Smart Timeouts
- Short timeouts (3s) for immediate UI responses
- Medium timeouts (8s) for network operations
- Long timeouts (15s) for complex operations

### 4. Performance Tracking
```javascript
const performanceTracker = {
  startTest(name) { this.startTime = Date.now(); },
  endTest(name) { 
    const duration = Date.now() - this.startTime;
    console.log(`Test ${name}: ${duration}ms`);
    return duration;
  }
};
```

## Troubleshooting Guide

### Common Issues and Solutions

#### 1. Element Not Found Errors
**Symptoms**: `Element not found` errors during test execution
**Causes**: 
- Insufficient wait times
- Element not in viewport
- TestID mismatch

**Solutions**:
```javascript
// Increase wait time
await waitForElement('elementId', 10000);

// Scroll to reveal element
await element(by.id('scrollContainer')).scroll(200, 'down');
await waitForElement('elementId', 5000);

// Verify testID exists in component
await expectElementVisible('elementId');
```

#### 2. Sequential Flow Violations
**Symptoms**: Tests fail when trying to access later steps
**Causes**: 
- Incomplete previous step data
- Navigation logic preventing progression

**Solutions**:
```javascript
// Always complete prerequisite steps
await completeAddressSteps();
await completeFurnitureStep();
// Now safe to test datetime step
```

#### 3. Scroll Overshooting
**Symptoms**: Target elements scroll past viewport
**Causes**: 
- Large scroll distances
- Fast scroll execution

**Solutions**:
```javascript
// Use incremental scrolling
await element(by.id('container')).scroll(150, 'down');
await sleep(500);
await element(by.id('container')).scroll(150, 'down');
```

#### 4. Network Timeout Issues
**Symptoms**: Tests fail on network-dependent operations
**Causes**: 
- Slow API responses
- Network connectivity issues

**Solutions**:
```javascript
// Extend timeouts for network operations
await waitForElement('networkElement', 15000);

// Add retry logic
for (let i = 0; i < 3; i++) {
  try {
    await tapId('networkButton');
    break;
  } catch (error) {
    if (i === 2) throw error;
    await sleep(2000);
  }
}
```

### Debug Commands

#### 1. Element Inspection
```javascript
// Check if element exists
await expect(element(by.id('testId'))).toExist();

// Check if element is visible
await expect(element(by.id('testId'))).toBeVisible();

// Get element attributes
const attributes = await element(by.id('testId')).getAttributes();
```

#### 2. Screenshot Capture
```javascript
// Capture screenshot on failure
await device.takeScreenshot('failure-screenshot');
```

#### 3. Performance Monitoring
```javascript
// Log test execution times
console.log('Performance Results:', performanceTracker.getResults());
```

## Setup Instructions

### 1. Prerequisites
- Detox framework installed and configured
- React Native app with home moving service
- Test data and user accounts configured

### 2. Test Execution
```bash
# Run all home moving tests
detox test remotes/service-home-moving/e2e/HomeMovingServiceSequentialFlow.e2e.js

# Run specific test suite
detox test --grep "Step 1: Old Home Address"

# Run with performance monitoring
detox test --verbose
```

### 3. Configuration
- Ensure `step-definition.js` is properly configured
- Verify test data constants match app configuration
- Check timeout values match app performance characteristics

This technical documentation provides comprehensive guidance for maintaining and extending the Home Moving Service E2E test suite while ensuring optimal performance and reliability.
