export const USER = {
  _id: 'userId_**********',
  services: {
    password: {
      bcrypt: '$2a$10$JB6kMoIYpyxO2ROr7C9Zc.azIQI1w1orh9K5J7D03etyYuWKZefvq',
    },
  },
  username: '***********',
  language: 'vi',
  referralCode: 'ASKER_**********',
  phone: '**********',
  isoCode: 'VN',
  taskDone: 0,
  totalTaskDone: 0,
  countryCode: '+84',
  fAccountId: 'financialAccount_**********',
  avgRating: 0,
  status: 'ACTIVE',
  type: 'ASKER',
  name: '<PERSON>er',
  avatar:
    'https://img.docbao.vn/images/uploads/2019/06/30/xa-hoi/vo-ngoc-tran-4.jpg',
  appVersion: '4.0.9',
  workingPlaces: [
    { country: 'VN', city: '<PERSON><PERSON>', district: 'Quận 9' },
    { country: 'VN', city: '<PERSON><PERSON>', district: 'Quận 11' },
    { country: 'VN', city: 'Hồ Chí Minh', district: 'Quận 10' },
    { country: 'VN', city: 'Hồ Chí Minh', district: 'Bình Tân' },
  ],
  score: 10,
  updatePrivacyPolicyAt: '2025-07-13T04:32:56.247Z',
  locations: [
    {
      _id: 'x6001bc7692559edd072cf7e4f',
      lat: 10.7331278,
      lng: 106.706233,
      country: 'VN',
      city: 'Hồ Chí Minh',
      district: 'Quận 10',
      address:
        'Công ty TNHH bTaskee, Hẻm 284/25 Lý Thường Kiệt, phường 14, Quận 10, Hồ Chí Minh, Việt Nam',
      contact: 'Asker',
      phoneNumber: '**********',
      shortAddress: '284/25 Lý Thường Kiệt',
      countryCode: '+84',
      isoCode: 'VN',
      homeType: 'HOME',
      description: 'My Task',
    },
  ],
  hospitalLocations: [
    {
      lng: 106.6595542,
      city: 'Hồ Chí Minh',
      country: 'VN',
      countryCode: '+84',
      lat: 10.7576886,
      district: 'Quận 5',
      isoCode: 'VN',
      phoneNumber: '**********',
      description: 'My Task',
      shortAddress: 'Cho ray',
      _id: 'x617279efa907c3fb005d145d',
      address:
        'Cho Ray Hospital, Nguyễn Chí Thanh, phường 12, Quận 5, Thành phố Hồ Chí Minh, Việt Nam',
    },
  ],
  homeMovingLocations: [
    {
      _id: 'x6001bc7692559edd072cf7',
      lat: 10.7331278,
      lng: 106.706233,
      country: 'VN',
      city: 'Hồ Chí Minh',
      district: 'Quận 10',
      address: 'bTaskee, D1',
      contact: 'Asker',
      phoneNumber: '**********',
      shortAddress: '123 Nhà Phố Lý Thường Kiệt',
      countryCode: '+84',
      isoCode: 'VN',
      homeType: 'HOME',
      description: '123',
    },
    {
      _id: 'x6001bc7559edd072cf7e4f',
      lat: 11.7331278,
      lng: 106.706233,
      country: 'VN',
      city: 'Hồ Chí Minh',
      district: 'Quận 10',
      address: 'Chung cư Lý Thường Kiệt',
      contact: 'Asker',
      phoneNumber: '**********',
      shortAddress: '456 Chung cư Lý Thường Kiệt',
      countryCode: '+84',
      isoCode: 'VN',
      homeType: 'APARTMENT',
      description: '456',
    },
    {
      _id: 'x6001bc7692559edd072cf7e4f',
      lat: 12.7331278,
      lng: 106.706233,
      country: 'VN',
      city: 'Hồ Chí Minh',
      district: 'Quận 10',
      address: 'Biệt thự Lý Thường Kiệt',
      contact: 'Asker',
      phoneNumber: '**********',
      shortAddress: '789 Biệt thự Lý Thường Kiệt',
      countryCode: '+84',
      isoCode: 'VN',
      homeType: 'VILLA',
      description: '789',
    },
    {
      _id: 'x6001bc7559edd077e4f',
      lat: 15.7331278,
      lng: 106.706233,
      country: 'VN',
      city: 'Đà Lạt',
      district: 'Quận 10',
      address: 'Chung cư Đà Lạt',
      contact: 'Asker',
      phoneNumber: '**********',
      shortAddress: '159 Chung cư Đà Lạt',
      countryCode: '+84',
      isoCode: 'VN',
      homeType: 'APARTMENT',
      description: '159',
    },
  ],
};
module.exports = {
  USER,
};
