/**
 * Home Moving Service Sequential Flow E2E Tests
 *
 * Comprehensive E2E test suite for React Native home moving service booking application
 * following strict sequential flow: Address Detail Step 1 → Address Detail Step 2 → Choose Furniture → Choose Duration → Overview → Payment/Confirmation
 *
 * Requirements:
 * - TestID-only selectors (no text expectations)
 * - Sequential flow compliance (cannot skip steps)
 * - Proper scrolling behavior for viewport management
 * - Performance optimized targeting 3-5 minute execution time
 * - Enhanced testID coverage for all interactive elements
 * - Scroll-to-reveal patterns for viewport management
 * - Comprehensive validation for complete home moving booking flow
 *
 * <AUTHOR> Automation Engineer
 * @framework Detox
 */

const { device, element, by, waitFor, expect } = require('detox');
const {
  initData,
  tapId,
  waitForElement,
  expectElementVisible,
  swipe,
  reloadApp,
  typeToTextField,
  sleep,
  ADDRESS_KEY,
} = require('./step-definition');

// Performance tracking utility
const performanceTracker = {
  startTime: null,
  testResults: {},

  startTest(testName) {
    this.startTime = Date.now();
    console.log(`🚀 Starting test: ${testName}`);
  },

  endTest(testName) {
    const duration = Date.now() - this.startTime;
    this.testResults[testName] = duration;
    console.log(`✅ Test completed: ${testName} in ${duration}ms`);
    return duration;
  },

  getResults() {
    return this.testResults;
  },
};

// Test data constants
const TEST_DATA = {
  OLD_HOME: {
    address: 'bTaskee, D1',
    homeType: 'Chung cư / Căn hộ',
    acreage: '<15m2',
    transport: 'Vận chuyển thang bộ',
    floors: '1 tầng',
    alley: 'Nhà trong hẻm',
    distance: '100m',
  },
  NEW_HOME: {
    address: 'Cho Ray Hospital',
    homeType: 'Nhà riêng',
    acreage: '15-30m2',
  },
  USER: {
    phone: '0834567890',
    name: 'HomeMovingTester',
    isoCode: 'VN',
    oldUser: true,
  },
};

const ASKER = {
  isoCode: 'VN',
  phone: '0834567890',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
};
const TASKER1 = {
  isoCode: 'VN',
  phone: '0834567891',
  name: 'Tasker',
  type: 'TASKER',
  status: 'ACTIVE',
  oldUser: true,
};

const LOCATION_HOME = {
  _id: 'x6001bc7692559edd072cf7',
  lat: 10.7331278,
  lng: 106.706233,
  country: 'VN',
  city: 'Hồ Chí Minh',
  district: 'Quận 10',
  address: ADDRESS_KEY.HCM,
  contact: 'Asker',
  phoneNumber: '0834567890',
  shortAddress: '123 Nhà Phố Lý Thường Kiệt',
  countryCode: '+84',
  isoCode: 'VN',
  homeType: 'HOME',
  description: '123',
};

const LOCATION_APARTMENT = {
  _id: 'x6001bc7559edd072cf7e4f',
  lat: 11.7331278,
  lng: 106.706233,
  country: 'VN',
  city: 'Hồ Chí Minh',
  district: 'Quận 10',
  address: 'Chung cư Lý Thường Kiệt',
  contact: 'Asker',
  phoneNumber: '0834567890',
  shortAddress: '456 Chung cư Lý Thường Kiệt',
  countryCode: '+84',
  isoCode: 'VN',
  homeType: 'APARTMENT',
  description: '456',
};

const LOCATION_DL = {
  _id: 'x6001bc7559edd077e4f',
  lat: 15.7331278,
  lng: 106.706233,
  country: 'VN',
  city: 'Đà Lạt',
  district: 'Quận 10',
  address: 'Chung cư Đà Lạt',
  contact: 'Asker',
  phoneNumber: '0834567890',
  shortAddress: '159 Chung cư Đà Lạt',
  countryCode: '+84',
  isoCode: 'VN',
  homeType: 'APARTMENT',
  description: '159',
};

const LOCATION_VILLA = {
  _id: 'x6001bc7692559edd072cf7e4f',
  lat: 12.7331278,
  lng: 106.706233,
  country: 'VN',
  city: 'Hồ Chí Minh',
  district: 'Quận 10',
  address: 'Biệt thự Lý Thường Kiệt',
  contact: 'Asker',
  phoneNumber: '0834567890',
  shortAddress: '789 Biệt thự Lý Thường Kiệt',
  countryCode: '+84',
  isoCode: 'VN',
  homeType: 'VILLA',
  description: '789',
};
describe('Home Moving Service Sequential Flow', () => {
  beforeEach(async () => {
    performanceTracker.startTest('setup');

    // Reset app state and data for consistent testing
    await initData('resetData');
    await initData('user/createUser', [ASKER, TASKER1]);
    await initData('user/updateUser', [
      {
        phone: ASKER.phone,
        isoCode: ASKER.isoCode,
        dataUpdate: {
          homeMovingLocations: [
            LOCATION_HOME,
            LOCATION_APARTMENT,
            LOCATION_VILLA,
            LOCATION_DL,
          ],
        },
      },
    ]);
    await initData('update-user/financialAccount', {
      phone: ASKER.phone,
      isoCode: 'VN',
      financialAccountData: { FMainAccount: ********* },
    });
    await device.reloadReactNative();
  });

  describe('Step 1: Old Home Address Detail Configuration', () => {
    it.only('should display old home address selection and allow configuration', async () => {
      performanceTracker.startTest('step1-old-home-address');

      await waitForElement('scrollChooseAddress', 8000);
      // Verify we're on step 1 (old home address)
      await expectElementVisible('scrollChooseAddress');
      await expectElementVisible('addNewAddressBtnHomeMoving');

      // Try to select existing address
      await waitForElement('addressItem-0', 3000);
      await tapId('addressItem-0');

      // Configure home details
      await waitForElement('AddressDetailHomeMovingScrollView', 8000);
      await expectElementVisible('AddressDetailHomeMovingScrollView');

      // Scroll to reveal home type options
      await element(by.id('AddressDetailHomeMovingScrollView')).scroll(
        200,
        'down',
      );
      await sleep(1000);

      // Select home type
      await tapId('homeTypeSelector');
      await waitForElement('homeTypeModal', 3000);
      await tapId('homeType-apartment');
      await tapId('confirmHomeTypeButton');

      // Select acreage
      await element(by.id('AddressDetailHomeMovingScrollView')).scroll(
        200,
        'down',
      );
      await tapId('acreageSelector');
      await waitForElement('acreageModal', 3000);
      await tapId('acreage-small');
      await tapId('confirmAcreageButton');

      // Configure transport options
      await element(by.id('AddressDetailHomeMovingScrollView')).scroll(
        300,
        'down',
      );
      await tapId('transportStairsButton');
      await tapId('floors-1');
      await tapId('alleyAccessButton');
      await tapId('distance-100m');

      // Proceed to next step
      await element(by.id('AddressDetailHomeMovingScrollView')).scroll(
        400,
        'down',
      );
      await waitForElement('btnNextMoving', 3000);
      await tapId('btnNextMoving');

      // Verify navigation to step 2
      await waitForElement('AddressDetailHomeMovingScrollView', 8000);

      const duration = performanceTracker.endTest('step1-old-home-address');
      expect(duration).toBeLessThan(60000); // Should complete within 1 minute
    });

    it('should handle address validation and error states', async () => {
      performanceTracker.startTest('step1-validation');

      // Verify required field validation
      await expectElementVisible('scrollChooseAddress');

      // Try to proceed without selecting address (should fail)
      try {
        await tapId('btnNextMoving');
        // Should not proceed without address selection
        await expectElementVisible('scrollChooseAddress'); // Still on same step
      } catch (error) {
        // Expected behavior - cannot proceed without address
      }

      performanceTracker.endTest('step1-validation');
    });
  });

  describe('Step 2: New Home Address Detail Configuration', () => {
    it('should configure new home address after completing step 1', async () => {
      performanceTracker.startTest('step2-new-home-address');

      // Complete Step 1 first
      await expectElementVisible('scrollChooseAddress');
      await tapId('addNewAddressBtnHomeMoving');
      await waitForElement('txtInputAddress', 5000);
      await typeToTextField('txtInputAddress', TEST_DATA.OLD_HOME.address);
      await tapId('confirmAddressButton');

      await waitForElement('AddressDetailHomeMovingScrollView', 8000);
      await element(by.id('AddressDetailHomeMovingScrollView')).scroll(
        200,
        'down',
      );
      await tapId('homeTypeSelector');
      await tapId('homeType-apartment');
      await tapId('acreageSelector');
      await tapId('acreage-small');
      await tapId('btnNextMoving');

      // Now on Step 2 - New Home Address
      await waitForElement('scrollChooseAddress', 8000);
      await expectElementVisible('addNewAddressBtnHomeMoving');

      // Add new home address
      await tapId('addNewAddressBtnHomeMoving');
      await waitForElement('txtInputAddress', 5000);
      await typeToTextField('txtInputAddress', TEST_DATA.NEW_HOME.address);
      await tapId('confirmAddressButton');

      // Configure new home details
      await waitForElement('AddressDetailHomeMovingScrollView', 8000);
      await element(by.id('AddressDetailHomeMovingScrollView')).scroll(
        200,
        'down',
      );

      await tapId('homeTypeSelector');
      await tapId('homeType-house');
      await tapId('acreageSelector');
      await tapId('acreage-medium');

      // Proceed to furniture selection
      await element(by.id('AddressDetailHomeMovingScrollView')).scroll(
        400,
        'down',
      );
      await tapId('btnNextMoving');

      // Verify navigation to step 3 (furniture selection)
      await waitForElement('ChooseFurnitureHomeMovingScrollView', 8000);
      await expectElementVisible('ChooseFurnitureHomeMovingScrollView');

      const duration = performanceTracker.endTest('step2-new-home-address');
      expect(duration).toBeLessThan(90000); // Should complete within 1.5 minutes
    });
  });

  describe('Step 3: Furniture Selection', () => {
    it('should allow furniture selection and configuration', async () => {
      performanceTracker.startTest('step3-furniture-selection');

      // Complete Steps 1 & 2 first (abbreviated for performance)
      await completeAddressSteps();

      // Now on Step 3 - Furniture Selection
      await waitForElement('ChooseFurnitureHomeMovingScrollView', 8000);
      await expectElementVisible('ChooseFurnitureHomeMovingScrollView');

      // Scroll to reveal furniture options
      await element(by.id('ChooseFurnitureHomeMovingScrollView')).scroll(
        300,
        'down',
      );

      // Select furniture items
      await tapId('furnitureItemBtn-0'); // First furniture item
      await tapId('plusBtnFurnitureItem-0'); // Increase quantity
      await waitForElement('quantityTxtFurnitureItem-0', 2000);

      await element(by.id('ChooseFurnitureHomeMovingScrollView')).scroll(
        200,
        'down',
      );
      await tapId('furnitureItemBtn-1'); // Second furniture item
      await tapId('plusBtnFurnitureItem-1');

      // Configure furniture options if available
      try {
        await tapId('optionBtnFurnitureItem-0-0');
        await waitForElement('furnitureOptionModal', 3000);
        await tapId('confirmFurnitureOptionButton');
      } catch (error) {
        // No options available for this furniture
      }

      // Proceed to duration selection
      await element(by.id('ChooseFurnitureHomeMovingScrollView')).scroll(
        400,
        'down',
      );
      await tapId('btnNextMoving');

      // Verify navigation to step 4 (duration selection)
      await waitForElement('ChooseDurationHomeMovingScrollView', 8000);
      await expectElementVisible('datePickerHomeMoving');

      const duration = performanceTracker.endTest('step3-furniture-selection');
      expect(duration).toBeLessThan(75000); // Should complete within 1.25 minutes
    });

    it('should handle furniture quantity changes and validation', async () => {
      performanceTracker.startTest('step3-furniture-validation');

      await completeAddressSteps();
      await waitForElement('ChooseFurnitureHomeMovingScrollView', 8000);

      // Test quantity controls
      await element(by.id('ChooseFurnitureHomeMovingScrollView')).scroll(
        300,
        'down',
      );
      await tapId('plusBtnFurnitureItem-0');
      await tapId('plusBtnFurnitureItem-0'); // Increase to 2
      await expectElementVisible('quantityTxtFurnitureItem-0');

      await tapId('minusBtnFurnitureItem-0'); // Decrease to 1
      await expectElementVisible('quantityTxtFurnitureItem-0');

      // Verify can proceed with or without furniture
      await element(by.id('ChooseFurnitureHomeMovingScrollView')).scroll(
        400,
        'down',
      );
      await tapId('btnNextMoving');
      await waitForElement('ChooseDurationHomeMovingScrollView', 8000);

      performanceTracker.endTest('step3-furniture-validation');
    });
  });

  describe('Step 4: Duration and DateTime Selection', () => {
    it('should allow date and time selection with proper validation', async () => {
      performanceTracker.startTest('step4-datetime-selection');

      // Complete previous steps
      await completeAddressSteps();
      await completeFurnitureStep();

      // Now on Step 4 - Duration/DateTime Selection
      await waitForElement('ChooseDurationHomeMovingScrollView', 8000);
      await expectElementVisible('datePickerHomeMoving');
      await expectElementVisible('timePickerHomeMoving');

      // Select date (tomorrow)
      await tapId('datePickerHomeMoving');
      await waitForElement('datePickerModal', 3000);
      await tapId('tomorrowDateOption');
      await tapId('confirmDateButton');

      // Select time
      await element(by.id('ChooseDurationHomeMovingScrollView')).scroll(
        200,
        'down',
      );
      await tapId('timePickerHomeMoving');
      await waitForElement('timePickerModal', 3000);
      await tapId('time-09-00'); // 9:00 AM
      await tapId('confirmTimeButton');

      // Add notes if needed
      await element(by.id('ChooseDurationHomeMovingScrollView')).scroll(
        300,
        'down',
      );
      try {
        await tapId('notePostTaskHomeMoving');
        await typeToTextField(
          'notePostTaskHomeMoving',
          'Please handle furniture carefully',
        );
      } catch (error) {
        // Notes field might not be available
      }

      // Proceed to overview
      await element(by.id('ChooseDurationHomeMovingScrollView')).scroll(
        400,
        'down',
      );
      await tapId('btnNextMoving');

      // Verify navigation to step 5 (overview)
      await waitForElement('OverviewHomeMovingScrollView', 8000);
      await expectElementVisible('shortAddressTxtOldHomeDetail');

      const duration = performanceTracker.endTest('step4-datetime-selection');
      expect(duration).toBeLessThan(60000); // Should complete within 1 minute
    });

    it('should validate minimum date and time restrictions', async () => {
      performanceTracker.startTest('step4-datetime-validation');

      await completeAddressSteps();
      await completeFurnitureStep();
      await waitForElement('ChooseDurationHomeMovingScrollView', 8000);

      // Try to select past date (should be disabled)
      await tapId('datePickerHomeMoving');
      await waitForElement('datePickerModal', 3000);

      // Past dates should be disabled - verify current/future dates are selectable
      await expectElementVisible('todayDateOption');
      await tapId('todayDateOption');
      await tapId('confirmDateButton');

      // Verify time picker shows appropriate options
      await tapId('timePickerHomeMoving');
      await waitForElement('timePickerModal', 3000);
      await expectElementVisible('availableTimeSlots');
      await tapId('cancelTimeButton');

      performanceTracker.endTest('step4-datetime-validation');
    });
  });

  describe('Step 5: Overview and Confirmation', () => {
    it('should display complete booking overview with edit capabilities', async () => {
      performanceTracker.startTest('step5-overview');

      // Complete all previous steps
      await completeAddressSteps();
      await completeFurnitureStep();
      await completeDateTimeStep();

      // Now on Step 5 - Overview
      await waitForElement('OverviewHomeMovingScrollView', 8000);
      await expectElementVisible('shortAddressTxtOldHomeDetail');
      await expectElementVisible('shortAddressTxtNewHomeDetail');

      // Verify address information is displayed
      await expectElementVisible('homeTypeTxtOldHomeDetail');
      await expectElementVisible('homeTypeTxtNewHomeDetail');

      // Scroll to see all overview content
      await element(by.id('OverviewHomeMovingScrollView')).scroll(300, 'down');

      // Verify furniture information if selected
      try {
        await expectElementVisible('furnitureOverviewSection');
      } catch (error) {
        // No furniture selected
      }

      // Verify date/time information
      await expectElementVisible('dateTimeOverviewSection');

      // Test edit functionality
      await element(by.id('OverviewHomeMovingScrollView')).scroll(200, 'up');
      await tapId('editOldHomeDetailButton');

      // Should navigate back to step 1
      await waitForElement('scrollChooseAddress', 5000);

      // Navigate back to overview
      await tapId('btnNextMoving'); // Step 1 to 2
      await tapId('btnNextMoving'); // Step 2 to 3
      await tapId('btnNextMoving'); // Step 3 to 4
      await tapId('btnNextMoving'); // Step 4 to 5

      // Proceed to payment
      await waitForElement('OverviewHomeMovingScrollView', 8000);
      await element(by.id('OverviewHomeMovingScrollView')).scroll(400, 'down');
      await tapId('btnNextMoving');

      // Verify navigation to payment screen
      await waitForElement('scrollViewStep4', 8000);
      await expectElementVisible('btnSubmitPostTask');

      const duration = performanceTracker.endTest('step5-overview');
      expect(duration).toBeLessThan(90000); // Should complete within 1.5 minutes
    });
  });

  describe('Final Step: Payment and Confirmation', () => {
    it('should complete payment flow and submit booking', async () => {
      performanceTracker.startTest('final-payment-confirmation');

      // Complete all previous steps
      await completeFullFlow();

      // Now on Payment/Confirmation screen
      await waitForElement('scrollViewStep4', 8000);
      await expectElementVisible('btnSubmitPostTask');

      // Verify booking details are displayed
      await expectElementVisible('locationConfirmSection');
      await expectElementVisible('taskDetailSection');

      // Scroll to see payment options
      await element(by.id('scrollViewStep4')).scroll(300, 'down');

      // Select payment method
      try {
        await tapId('paymentMethodSelector');
        await waitForElement('paymentMethodModal', 3000);
        await tapId('paymentMethod-wallet');
        await tapId('confirmPaymentMethodButton');
      } catch (error) {
        // Default payment method might be pre-selected
      }

      // Accept terms and conditions
      await element(by.id('scrollViewStep4')).scroll(400, 'down');
      await tapId('checkboxPolicyPostTaskHomeMoving');
      await expectElementVisible('btnSubmitPostTask');

      // Submit booking
      await tapId('btnSubmitPostTask');

      // Verify success navigation
      await waitForElement('postTaskSuccessScreen', 10000);
      await expectElementVisible('bookingConfirmationMessage');

      const duration = performanceTracker.endTest('final-payment-confirmation');
      expect(duration).toBeLessThan(30000); // Should complete within 30 seconds
    });

    it('should validate payment requirements and policy acceptance', async () => {
      performanceTracker.startTest('payment-validation');

      await completeFullFlow();
      await waitForElement('scrollViewStep4', 8000);

      // Try to submit without accepting policy (should be disabled)
      await element(by.id('scrollViewStep4')).scroll(400, 'down');
      await expectElementVisible('btnSubmitPostTask');

      // Button should be disabled without policy acceptance
      try {
        await tapId('btnSubmitPostTask');
        // Should not proceed without policy acceptance
        await expectElementVisible('scrollViewStep4'); // Still on same screen
      } catch (error) {
        // Expected - button should be disabled
      }

      // Accept policy and verify button becomes enabled
      await tapId('checkboxPolicyPostTaskHomeMoving');
      await expectElementVisible('btnSubmitPostTask'); // Should now be enabled

      performanceTracker.endTest('payment-validation');
    });
  });

  describe('Complete Sequential Flow Tests', () => {
    it('should complete entire home moving booking flow successfully', async () => {
      performanceTracker.startTest('complete-sequential-flow');

      // Step 1: Old Home Address
      await expectElementVisible('scrollChooseAddress');
      await tapId('addNewAddressBtnHomeMoving');
      await waitForElement('txtInputAddress', 5000);
      await typeToTextField('txtInputAddress', TEST_DATA.OLD_HOME.address);
      await tapId('confirmAddressButton');

      await waitForElement('AddressDetailHomeMovingScrollView', 8000);
      await element(by.id('AddressDetailHomeMovingScrollView')).scroll(
        200,
        'down',
      );
      await tapId('homeTypeSelector');
      await tapId('homeType-apartment');
      await tapId('acreageSelector');
      await tapId('acreage-small');
      await tapId('btnNextMoving');

      // Step 2: New Home Address
      await waitForElement('scrollChooseAddress', 8000);
      await tapId('addNewAddressBtnHomeMoving');
      await waitForElement('txtInputAddress', 5000);
      await typeToTextField('txtInputAddress', TEST_DATA.NEW_HOME.address);
      await tapId('confirmAddressButton');

      await waitForElement('AddressDetailHomeMovingScrollView', 8000);
      await element(by.id('AddressDetailHomeMovingScrollView')).scroll(
        200,
        'down',
      );
      await tapId('homeTypeSelector');
      await tapId('homeType-house');
      await tapId('btnNextMoving');

      // Step 3: Furniture Selection
      await waitForElement('ChooseFurnitureHomeMovingScrollView', 8000);
      await element(by.id('ChooseFurnitureHomeMovingScrollView')).scroll(
        300,
        'down',
      );
      await tapId('plusBtnFurnitureItem-0');
      await tapId('btnNextMoving');

      // Step 4: DateTime Selection
      await waitForElement('ChooseDurationHomeMovingScrollView', 8000);
      await tapId('datePickerHomeMoving');
      await tapId('tomorrowDateOption');
      await tapId('timePickerHomeMoving');
      await tapId('time-09-00');
      await tapId('btnNextMoving');

      // Step 5: Overview
      await waitForElement('OverviewHomeMovingScrollView', 8000);
      await expectElementVisible('shortAddressTxtOldHomeDetail');
      await expectElementVisible('shortAddressTxtNewHomeDetail');
      await element(by.id('OverviewHomeMovingScrollView')).scroll(400, 'down');
      await tapId('btnNextMoving');

      // Final: Payment
      await waitForElement('scrollViewStep4', 8000);
      await element(by.id('scrollViewStep4')).scroll(400, 'down');
      await tapId('checkboxPolicyPostTaskHomeMoving');
      await tapId('btnSubmitPostTask');

      // Verify success
      await waitForElement('postTaskSuccessScreen', 10000);
      await expectElementVisible('bookingConfirmationMessage');

      const duration = performanceTracker.endTest('complete-sequential-flow');
      expect(duration).toBeLessThan(300000); // Should complete within 5 minutes

      // Log performance results
      console.log('🎯 Performance Results:', performanceTracker.getResults());
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle navigation without completing previous steps', async () => {
      performanceTracker.startTest('error-handling-navigation');

      // Verify sequential flow enforcement
      await expectElementVisible('scrollChooseAddress');

      // Try to access later steps directly (should not be possible due to PagerView control)
      // The app enforces sequential flow through step-by-step navigation

      // Verify back navigation works correctly
      await tapId('addNewAddressBtnHomeMoving');
      await waitForElement('txtInputAddress', 5000);
      await tapId('header-back'); // Go back
      await expectElementVisible('scrollChooseAddress'); // Should return to address selection

      performanceTracker.endTest('error-handling-navigation');
    });

    it('should handle network timeouts and retry scenarios', async () => {
      performanceTracker.startTest('error-handling-network');

      // Test with potential network delays
      await expectElementVisible('scrollChooseAddress');

      // Add longer timeouts for network-dependent operations
      await tapId('addNewAddressBtnHomeMoving');
      await waitForElement('txtInputAddress', 10000); // Extended timeout

      // Simulate slow network response
      await typeToTextField('txtInputAddress', TEST_DATA.OLD_HOME.address);
      await sleep(2000); // Simulate network delay
      await tapId('confirmAddressButton');

      await waitForElement('AddressDetailHomeMovingScrollView', 15000); // Extended timeout

      performanceTracker.endTest('error-handling-network');
    });
  });

  describe('Scrolling and Viewport Management', () => {
    it('should handle scroll-to-reveal patterns effectively', async () => {
      performanceTracker.startTest('scrolling-viewport-management');

      await completeAddressSteps();
      await waitForElement('ChooseFurnitureHomeMovingScrollView', 8000);

      // Test incremental scrolling patterns
      await element(by.id('ChooseFurnitureHomeMovingScrollView')).scroll(
        150,
        'down',
      );
      await sleep(500);
      await element(by.id('ChooseFurnitureHomeMovingScrollView')).scroll(
        150,
        'down',
      );
      await sleep(500);
      await element(by.id('ChooseFurnitureHomeMovingScrollView')).scroll(
        150,
        'down',
      );

      // Verify elements become visible after scrolling
      await expectElementVisible('furnitureItemBtn-0');
      await expectElementVisible('plusBtnFurnitureItem-0');

      // Test scroll-back functionality
      await element(by.id('ChooseFurnitureHomeMovingScrollView')).scroll(
        200,
        'up',
      );
      await sleep(500);
      await element(by.id('ChooseFurnitureHomeMovingScrollView')).scroll(
        200,
        'up',
      );

      performanceTracker.endTest('scrolling-viewport-management');
    });
  });
});

// Helper functions for test reusability
async function completeAddressSteps() {
  // Complete Step 1: Old Home
  await expectElementVisible('scrollChooseAddress');
  await tapId('addNewAddressBtnHomeMoving');
  await waitForElement('txtInputAddress', 5000);
  await typeToTextField('txtInputAddress', TEST_DATA.OLD_HOME.address);
  await tapId('confirmAddressButton');

  await waitForElement('AddressDetailHomeMovingScrollView', 8000);
  await element(by.id('AddressDetailHomeMovingScrollView')).scroll(200, 'down');
  await tapId('homeTypeSelector');
  await tapId('homeType-apartment');
  await tapId('acreageSelector');
  await tapId('acreage-small');
  await tapId('btnNextMoving');

  // Complete Step 2: New Home
  await waitForElement('scrollChooseAddress', 8000);
  await tapId('addNewAddressBtnHomeMoving');
  await waitForElement('txtInputAddress', 5000);
  await typeToTextField('txtInputAddress', TEST_DATA.NEW_HOME.address);
  await tapId('confirmAddressButton');

  await waitForElement('AddressDetailHomeMovingScrollView', 8000);
  await element(by.id('AddressDetailHomeMovingScrollView')).scroll(200, 'down');
  await tapId('homeTypeSelector');
  await tapId('homeType-house');
  await tapId('btnNextMoving');
}

async function completeFurnitureStep() {
  await waitForElement('ChooseFurnitureHomeMovingScrollView', 8000);
  await element(by.id('ChooseFurnitureHomeMovingScrollView')).scroll(
    300,
    'down',
  );
  await tapId('plusBtnFurnitureItem-0');
  await tapId('btnNextMoving');
}

async function completeDateTimeStep() {
  await waitForElement('ChooseDurationHomeMovingScrollView', 8000);
  await tapId('datePickerHomeMoving');
  await tapId('tomorrowDateOption');
  await tapId('timePickerHomeMoving');
  await tapId('time-09-00');
  await tapId('btnNextMoving');
}

async function completeFullFlow() {
  await completeAddressSteps();
  await completeFurnitureStep();
  await completeDateTimeStep();

  // Complete overview step
  await waitForElement('OverviewHomeMovingScrollView', 8000);
  await element(by.id('OverviewHomeMovingScrollView')).scroll(400, 'down');
  await tapId('btnNextMoving');
}
