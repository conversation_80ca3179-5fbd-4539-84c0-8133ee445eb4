{"name": "service-home-moving", "targets": {"start": {"executor": "nx:run-commands", "options": {"command": "yarn start", "cwd": "remotes/service-home-moving"}}, "install": {"executor": "nx:run-commands", "options": {"command": "yarn install", "cwd": "remotes/service-home-moving"}}, "build": {"executor": "nx:run-commands", "options": {"command": "yarn build", "cwd": "remotes/service-home-moving"}}, "deploy": {"executor": "nx:run-commands", "options": {"command": "yarn deploy", "cwd": "remotes/service-home-moving"}}, "reset": {"executor": "nx:run-commands", "options": {"command": "yarn reset", "cwd": "remotes/service-home-moving"}}}, "tags": ["scope:service-home-moving"]}