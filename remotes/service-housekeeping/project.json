{"name": "service-housekeeping", "targets": {"start": {"executor": "nx:run-commands", "options": {"command": "yarn start", "cwd": "remotes/service-housekeeping"}}, "install": {"executor": "nx:run-commands", "options": {"command": "yarn install", "cwd": "remotes/service-housekeeping"}}, "build": {"executor": "nx:run-commands", "options": {"command": "yarn build", "cwd": "remotes/service-housekeeping"}}, "deploy": {"executor": "nx:run-commands", "options": {"command": "yarn deploy", "cwd": "remotes/service-housekeeping"}}, "reset": {"executor": "nx:run-commands", "options": {"command": "yarn reset", "cwd": "remotes/service-housekeeping"}}}, "tags": ["scope:service-housekeeping"]}