# Business Design Document - Home Moving Service

## 1. Purpose

The Home Moving Service allows users to book professional home relocation tasks, guiding them through location setup, choose task details, scheduling, and payment in a seamless native experience. The experience supports flexible input for both current and new addresses, home types, special conditions like narrow alleys and multi-floor transport, detailed item specification (including disassembly of electronics), and transparent overview of all task details before payment.

## 2. Scope / Features

### Current Location

* **Address Selection and Configuration:** Comprehensive address input system allowing users to search, select, and configure their pickup location with map-based confirmation and detailed address entry
* **Property Type Selection:** Flexible property classification system supporting apartment/condominium and townhouse/villa categories with corresponding area specifications
* **Area Configuration:** Size-based pricing system with predefined area ranges for apartments and floor configurations for townhouses
* **Special Transportation Options:** Additional service configurations including staircase transportation, house in alley specifications with width selection and distance options, and parking garage transportation toggle

### New Location

* **Address Selection and Configuration:** Comprehensive address input system allowing users to search, select, and configure their destination location with map-based confirmation and detailed address entry
* **Same Building Option:** Specialized intra-building moving service for apartment residents requiring transportation within the same building structure with simplified configuration
* **Property Type Selection:** Flexible property classification system supporting apartment/condominium and townhouse/villa categories with corresponding area specifications
* **Special Transportation Options:** Additional service configurations including staircase transportation, house in alley specifications with width selection and distance options, and parking garage transportation toggle

### Select items to be transported

* **Oversized Furniture Visual Guide**: Interactive visual representation system displaying furniture size reference with 180cm measurement indicator and illustration of movers handling furniture to help users understand oversized item classification and preparation requirements.
* **Furniture Category Selection System**: Comprehensive furniture classification system supporting three main categories with expandable selection interface, quantity management, and optional photo documentation capabilities for accurate service estimation and preparation.
* **Specialized Disassembly Services**: Professional equipment disassembly and reassembly services with category-specific options including air conditioner disassembly with quantity selection and water heater tank disassembly with type specification (instant water heater, indirect water heater) and quantity management.
* **Standard Packaging Process Information**: Comprehensive packaging guidance system providing detailed step-by-step instructions for various item types including liquid goods, clothing, books, fragile items, electronics, and bulky furniture with professional packing techniques and material specifications.

### Choose Working Time

* **Date and Time Selection:** Flexible scheduling system allowing users to choose preferred appointment dates and times
* **Notes for Tasker:** Text-based communication system allowing users to provide specific instructions and requests to tasker

### Overview

* **Comprehensive Booking Summary Display:** Complete booking information review system displaying all user selections and configurations with organized categorization including address details, property specifications, transportation requirements, and furniture inventory
* **Total Price Calculation and Display:** Dynamic pricing summary system showing calculated total service cost based on all selected options and configurations with real-time updates
* **Navigation and Progression Control:** Booking workflow advancement system allowing users to proceed to payment confirmation or return to previous steps for modifications

### Confirm and Payment

* **Booking Confirmation:** Comprehensive booking summary and confirmation system displaying all service details before final commitment
* **Payment Processing:** Multi-channel payment system supporting various payment methods including digital wallets, cards, and alternative payment options
* **Promotion Management:** Discount and promotion code system allowing users to apply available offers and see immediate savings
* **Contact Updates:** Real-time contact information editing capabilities during the booking confirmation process for both current location and new location

## **3. Stakeholders**

* **End Users:** Customers booking home moving services
* **Taskers:** Service providers receiving tasks
* **Admin:** Oversee bookings, pricing, and service quality

## **4. Functional Requirements**

### 4.1 Current Location

* The system will navigate the user to the Choose location page to set up the initial location first:
  * **Multi-Input Address System:** Users can input addresses through three distinct methods:
    * GPS location detection for automatic current location identification and nearby address suggestion
    * Manual text entry with auto-complete and address suggestion functionality for precise address specification
    * Interactive map selection with pin-dropping capability for visual location picking and address confirmation
  * **Address Validation and Formatting:** The system validates the address. Users receive feedback if the address is invalid and is prevented to proceeding with booking. User will also have to provide house number to proceed.
  * After tapping on ‘Ok’, the user will be navigated back to the ‘Current location’ page.
* Users can tap "Add a new address" to initiate location selection process with navigation to Choose location screen
* **Property Type Classification System:** Users can select from comprehensive property categories:
  * Users can choose between "Apartment/Condominium" and "Townhouse/Villa" options with visual icons
  * The system displays corresponding area selectors based on property type selection
  * Users see "Change house type" link allowing modification of property type selection
* **Apartment Area Selection Interface:** Users can specify apartment size from predefined categories:
  * Users can select from six area ranges: <15m², 15m²–25m², 25m²–35m², 35m²–45m², 45m²–65m², 65m²–95m²
  * The system provides bedroom count indicators for relevant size categories (35m²–45m² shows "One-bedroom", 45m²–65m² shows "Two-bedroom", 65m²–95m² shows "Three-bedroom")
* **Townhouse Configuration System:** Users can specify townhouse structure from available options:
  * Users can select from five floor configurations: Bungalow (Up to 50m²), 1 ground + 1 floor (Up to 100m²), 1 ground + 2 floors (Up to 150m²), 1 ground + 3 floors (Up to 200m²), 1 ground + 4 floors (Up to 250m²)
  * The system displays area limits for each configuration option
  * The system shows pricing notice: "The current pricing applies to townhouses (used for residing) with a floor area less than 50m² and is not applicable to commercial townhouses."
* **Staircase Transportation Configuration:** Users can specify multi-floor transportation requirements:
  * Users can select from four floor options: 1 floor, 2 floors, 3 floors, 4 floors
  * The system provides fallback message: "If your demands are not listed in this category, please contact the customer service hotline for assistance"
* **House in Alley Specification System:** Users can configure narrow access requirements:
  * Users can adjust alley width using minus (-) and plus (+) buttons with minimum of 2 meters and maximum of 6 meters
  * Users can select distance from truck road to house from three options: 50m, 100m, 200m
  * The system provides guidance: "If your demands are greater, please describe them in the task note. bTaskee will contact you to provide support and a quotation."
* **Parking Garage Transportation Toggle:** Users can enable additional transportation service:
  * Users can toggle "Transportation from the parking garage" option with visual switch control
* **Service Information Access:** Users can access detailed service process information:
  * Users can tap "Home moving process" option to view comprehensive moving procedure
  * The system displays step-by-step moving process with detailed breakdown of activities
* **Navigation and Progression Control:** Users can control workflow advancement:
  * Users can tap "Proceed to the next step" button when all required selections are completed
  * The system validates all necessary information before allowing progression to next booking step

### 4.2 New Location

* The system will navigate the user to the Choose location page to set up the destination location:
  * **Multi-Input Address System:** Users can input addresses through three distinct methods:
    * GPS location detection for automatic current location identification and nearby address suggestion
    * Manual text entry with auto-complete and address suggestion functionality for precise address specification
    * Interactive map selection with pin-dropping capability for visual location picking and address confirmation
  * **Same Building Option:** Users can select "The same building" option when current location is set to "Apartment/Condominium":
    * The system displays "The same building" popup with service explanation and illustration
    * Users can tap "Continue" to confirm same building service or "Close" to return to address selection
    * When confirmed, the system automatically sets the destination address to match the current location address
  * **Address Validation and Formatting:** The system validates the address. Users receive feedback if the address is invalid and is prevented from proceeding with booking. User will also have to provide house number to proceed.
  * After tapping on 'Ok', the user will be navigated back to the 'New location' page.
* Users can tap "Add a new address" to initiate location selection process with navigation to Choose location screen
  * **Same Building Change Confirmation:** When users have selected same building service and tap "Select another address", the system displays a confirmation dialog:
    * Dialog shows "Change Confirmation" title with map illustration
    * Users can tap "Close" to cancel and preserve current same building configuration
    * Users can tap "Confirm" to proceed with address change, which clears all same building configurations and navigates to Choose location screen
* Users can tap "Select another address" to modify previously selected destination address with navigation to Choose location screen
* **Property Type Classification System:** Users can select from comprehensive property categories:
  * Users can choose between "Apartment/Condominium" and "Townhouse/Villa" options with visual icons
  * The system displays corresponding area selectors based on property type selection
  * Users see "Change house type" link allowing modification of property type selection
* **Staircase Transportation Configuration:** Users can specify multi-floor transportation requirements:
  * Users can select from four floor options: 1 floor, 2 floors, 3 floors, 4 floors
  * The system provides fallback message: "If your demands are not listed in this category, please contact the customer service hotline for assistance"
* **House in Alley Specification System:** Users can configure narrow access requirements:
  * Users can adjust alley width using minus (-) and plus (+) buttons with minimum of 2 meters and maximum of 6 meters
  * Users can select distance from truck road to house from three options: 50m, 100m, 200m
  * The system provides guidance: "If your demands are greater, please describe them in the task note. bTaskee will contact you to provide support and a quotation."
* **Parking Garage Transportation Toggle:** Users can enable additional transportation service:
  * Users can toggle "Transportation from the parking garage" option with visual switch control
* **Service Information Access:** Users can access detailed service process information:
  * Users can tap "Home moving process" option to view comprehensive moving procedure
  * The system displays step-by-step moving process with detailed breakdown of activities
* **Navigation and Progression Control:** Users can control workflow advancement:
  * Users can tap "Next" button when all required selections are completed
  * The system validates all necessary information before allowing progression to next booking step

### 4.3 Select items to be transported

* **Furniture Category Selection Interface** Users can select furniture items through expandable category system:
  * Users see three main furniture categories: "Removable furniture", "Solid furniture", and "Electronic devices"
  * Users can tap on any category to expand selection options
  * The system shows collapsed state by default for all categories
  * Users see green checkmark indicator when category is expanded and selected
* **Quantity Management System** Users can manage item quantities through interactive controls:
  * Users can adjust quantity using minus (-) and plus (+) buttons
  * Users can set quantity from 1 to 20 for each furniture category
  * The system automatically collapses category when quantity is decreased from 1 to 0
* **Optional Photo Documentation** Users can capture photos for better service preparation:
  * Users see "Take actual photos (Optional)" option with camera icon in dashed border
  * Users can tap to capture photos or select photos from their library of their actual furniture items
  * The photo capture feature is optional and does not prevent booking completion
* **Electronic Device Disassembly Services** Users can select specialized disassembly options for electronic devices:
  * Users can expand "Disassemble air conditioner" or "Disassemble water heater tank" option with gear icon
  * Users can set "Air conditioner", "Instant water heater", "Indirect water heater" or quantity from 0 to 20 units
  * The system enforces constraint that total disassembly quantities must be less than Electronic devices main category quantity
* **Standard Packaging Process Information** Users can access detailed packaging guidelines:
  * Users can tap "Standard packaging process" option with chat icon
  * Users see comprehensive packaging category list including:
    * "Liquid goods (cosmetics, shampoo, etc.)"
    * "Clothing, handbags, shoes"
    * "Books, documents, and office supplies"
    * "Fragile items (cups, plates, glassware, etc.)"
    * "Laptop, computer monitor, TV screen,..."
    * "Electronics, home appliances, and refrigeration appliances"
    * "Bulky furniture items"
  * Users can tap on any packaging category to view detailed step-by-step instructions

### 4.4 Choose Working Time

* **Calendar Date Selection:** Users interact with an interactive calendar interface governed by the following logic:
  * The calendar displays **14 consecutive days** starting from the day the user opens the calendar.
  * The **first two days** (i.e., **today and tomorrow**) are **visibly shown but disabled**—they cannot be tapped or selected.
  * Users can select any **available date from Day 3 onward** (e.g., if today is Monday, users can select from Wednesday to the following Monday).
  * The system prevents selection of any **past dates** or **dates beyond the 14-day window**.
  * Visual indicators mark **holidays** and **peak demand days**, helping users make informed booking decisions.
* **Time Selection Interface:** Users can utilize an intuitive time picker with comprehensive options:
  * Users see 12-hour format with clear AM/PM designation
  * Users can select 5-minute interval options for flexible scheduling
  * The system enforces minimum 1-hour advance booking requirement when users select current date (tasker start time must be at least 1 hour after booking time)
* **Notes for Tasker:** Users can utilize a robust text input system for instructions:
  * Users can leave the optional text field empty without preventing booking completion
  * The system enforces maximum character limit of 400 characters with real-time character counter and remaining count display

### 4.5 Overview

* **Address Summary and Management Interface:** Users can view and modify location information through comprehensive address display:
  * "Address" section header with complete current location and new location details
  * "Current location" and "New location" with full address display including street address, district, city, and country information
  * "Apartment / Condominium" or "Townhouse / Villa" based on previous selection
  * "Staircase transportation 1 floor", "House in the alley 100m (2m wide)", and "Transportation from the parking garage" when applicable
  * Users can tap green edit icon next to "Current location" or "New location" to modify address information and return to respective configuration screens
* **Furniture Inventory Review and Modification System:** Users can review and adjust selected furniture items through detailed inventory display:
  * Users see "Oversized furniture" section header with comprehensive furniture category breakdown:
    * "Electronic devices"
    * "Removable furniture"
    * "Solid furniture"
  * Users can tap green edit icon next to "Oversized furniture" to modify furniture selections and return to item selection screen
* **Working Time Information and Schedule Management:** Users can view and modify appointment scheduling through time configuration display:
  * "Working time" section header with complete scheduling information
  * Selected date and time with complete date and time information
  * Users can tap green edit icon next to "Working time" to modify schedule and return to time selection screen
* **Dynamic Pricing Display and Calculation System:** Users can view total service cost with comprehensive pricing information:
  * The system calculates total cost based on all selected services, property types, transportation requirements, furniture quantities, and scheduling preferences
  * The system updates pricing display when users make modifications to any booking components
* **Navigation and Workflow Control Interface:** Users can control booking progression and navigation through action buttons:
  * Users can tap back arrow to return to previous booking step (Choose Working Time)
  * Users can tap "Next" button in green format to proceed to Confirm and Payment step
  * The system validates all required information is complete before enabling progression to payment step

### 4.5 Confirm and Payment

* **Complete Booking Summary Display:** Users can review comprehensive booking information:
  * Users see complete address display with property type and contact information
  * Users see detailed service type breakdown
  * Users see scheduled date, time, and recurring schedule details
  * The system displays custom instruction notes if provided
* **Comprehensive Payment Method Support:** Users can select from multiple payment channels with robust processing:
  * Users can choose cash payment option with clear instructions for payment upon task completion
  * Users can use digital wallet integration including bPay, Momo, ZaloPay, ShopeePay,…. with secure connections
  * Users can process credit card payments for Visa and MasterCard with secure 3D authentication
  * Users can access buy-now-pay-later option through Kredivo with installment calculation and approval workflow
  * The system provides payment method validation and error handling for failed transactions
* **Advanced Promotion Code Management:** Users can access comprehensive promotion functionality:
  * Users can enter promotion codes in dedicated text input field with format validation
  * Users see automatic discount calculation and application to total pricing with immediate visual feedback
  * Users can remove promotion codes with pricing recalculation
* **Comprehensive Booking Validation and Confirmation Process:** The system ensures robust booking processing:
  * Multi-step validation ensuring all required fields are completed and properly formatted
  * The system performs final availability and pricing confirmation check before payment processing
  * Secure payment processing according to selected method with comprehensive error handling
  * The system automatically navigates users to Success Booking screen upon successful transaction completion
* **“Agree to the Terms and Conditions”** checkbox

## **5. Non-Functional Requirements**

* Mobile-first responsive design
* Real-time validation & feedback
* Localization (support Vietnamese & English)
* Secure data handling & storage

## **6. Acceptance Criteria (Testable Steps)**

### **6.1** Current Location

* **Choose Location Screen:**
  * Using GPS to automatically detect their current location.
  * Manually entering full address details.
  * Tapping on the map and confirming with "Pick this location."
* **Location Detail Input & Confirmation:** After selecting or adding a location, users are directed to a screen to:
  * Fill the House number field
  * Users can tap "OK" to confirm these address and contact details.
* **Select another address**
  * The user can choose to select another address by tapping on **Select another address**
  * Tapping it re-opens **Select Location**
* **Property Type Selection**
  * After saving an address, the **Select type of house** section displays two icons: **Apartment/Condominium** and **Townhouse/Villa**.
  * **Apartment/Condominium** reveals six area options (<15 m², 15–25 m², 25–35 m², 35–45 m², 45–65 m², 65–95 m²)
    * Bedroom labels are shown for relevant ranges (e.g. “35–45 m² – One-bedroom”).
  * **Townhouse/Villa** reveals five floor configurations (Bungalow up to 50 m²; 1 + 1 up to 100 m²; 1 + 2 up to 150 m²; 1 + 3 up to 200 m²; 1 + 4 up to 250 m²); and displays a residential-pricing notice.
  * A **Change house type** link returns the user to the initial type selection.
* **Staircase Transportation**
  * A **Staircase transportation** dropdown shows floor options (1–4 floors) with explanatory text.
* **House in the Alley**
  * A **House in the alley** dropdown provides:
    * **Alley width** control (–/+) from 2 m to 6 m.
    * Distance options (50 m, 100 m, 200 m).
  * Guidance text prompts users to add complex requirements in task notes for a custom quote.
* **Parking Garage Transportation**
  * A **Transportation from the parking garage** toggle lets users include garage pickup
* **Service Information Access**
  * Tapping **Home moving process** opens a detailed, step-by-step procedure view.
* **Proceed to Next Step**
  * The **Proceed to the next step** button is disabled until address, property type, and any mandatory options are set.
  * Once enabled, tapping it validates all inputs and advances to the next booking stage; missing or invalid fields show inline errors.

### **6.2** New Location

* **Choose Location Screen:**
  * Using GPS to automatically detect their current location.
  * Manually entering full address details.
  * Tapping on the map and confirming with "Pick this location."
  * Selecting "The same building" option when current location is "Apartment/Condominium" and confirming with "Continue" button.
* **Location Detail Input & Confirmation:**
  * After selecting or adding a location, users are directed to a screen to:
    * Fill the House number field.
    * Users can tap "OK" to confirm these address and contact details.
* **Select another address:**
  * The user can choose to select another address by tapping on **Select another address**.
  * Tapping it re-opens **Select Location**.
* **Same Building Service Configuration:**
  * When current location is set to "Apartment/Condominium", users see "The same building" option in Choose Location screen.
  * Tapping "The same building" displays popup with service explanation.
  * Users can tap "Continue" to confirm same building service or "Close" to return to address selection.
  * When "Continue" is selected, system automatically sets destination address to match current location address.
* **Same Building Change Confirmation Dialog:**
  * When user has selected "The same building" service and taps "Select another address", the system displays "Change Confirmation" dialog.
  * Dialog provides two action buttons:
    * "Close" button (left, gray styling) allows user to cancel change and preserve existing same building configuration.
    * "Confirm" button (right, green styling) proceeds with address change.
  * When "Close" is tapped:
    * Dialog dismisses and user returns to New Location screen.
    * All existing same building configurations remain intact.
    * Current address and property settings are preserved.
  * When "Confirm" is tapped:
    * Dialog dismisses and system clears all same building related configurations.
    * System removes destination address, area selections, and staircase transportation settings.
    * User is navigated to Choose location screen for fresh address selection.
* If the user has selected **"The same building"**, the screen only shows:
  * **Staircase Transportation**
    * A **Staircase transportation** dropdown shows floor options (1–4 floors) with explanatory text: "Selecting the right number of floors helps arrange personnel appropriately. Note that the number of floors will affect the total service fee."
* If the user has **not** selected **"The same building"**, the screen shows:
  * **Property Type Selection**
    * After saving an address, the **Select type of house** section displays two icons: **Apartment/Condominium** and **Townhouse/Villa**.
    * A **Change house type** link returns the user to the initial type selection.
  * **Staircase Transportation**
    * A **Staircase transportation** dropdown shows floor options (1–4 floors) with explanatory text: "Selecting the right number of floors helps arrange personnel appropriately. Note that the number of floors will affect the total service fee."
  * **House in the Alley**
    * A **House in the alley** dropdown provides:
      * **Select the width of the alley** control (–/+) from 2 m to 6 m.
      * **Select distance from the truck road to your house** options (50m, 100m, 200m).
    * Guidance text prompts users to add complex requirements in task notes for a custom quote.
  * **Parking Garage Transportation**
    * A **Transportation from the parking garage** toggle lets users include garage pickup.
  * **Service Information Access**
    * Tapping **Home moving process** opens a detailed, step-by-step procedure view.
* **Proceed to Next Step**
  * The **Proceed to the next step** button is disabled until address, property type, and any mandatory options are set.
  * Once enabled, tapping it validates all inputs and advances to the next booking stage; missing or invalid fields show inline errors.

### 6.3 Select items to be transported

* **Furniture Category Initial State** The system displays three furniture categories in collapsed state:
  * "Removable furniture" section shows with dropdown arrow and example text "Example: Bed, table, bookshelf, ..."
  * "Solid furniture" section shows with dropdown arrow and example text "Example: Altar, ancestral worship cabinet, Bud..."
  * "Electronic devices" section shows with dropdown arrow and example text "Example: Refrigerator, TV, Washing Machine, W..."
* **Category Expansion and Selection** When the user taps on any furniture category:
  * The selected category expands to show quantity selector and photo optio
  * Quantity selector shows minus button, number "1", and plus button
  * "Take actual photos (Optional)" option appears in dashed border with camera icon
* **Quantity Adjustment Behavior** When the user adjusts quantities using the quantity selector:
  * Tapping plus (+) button increases quantity by 1 up to maximum of 20
  * Tapping minus (-) button decreases quantity by 1 down to minimum of 0
  * Current quantity displays in center of selector
  * When quantity reaches 0, the category automatically collapses and returns to default state
* **Electronic Devices Disassembly Options** When the user expands Electronic devices category and sets quantity above 0:
  * When user taps on "Disassemble air conditioner", it expands to show:
    * "Air conditioner" quantity selector with initial value of 0
  * When user taps on "Disassemble water heater tank", it expands to show:
    * "Instant water heater" quantity selector with initial value of 0
    * "Indirect water heater" quantity selector with initial value of 0
  * Tapping plus (+) button increases quantity by 1 up to maximum of 20
  * Tapping minus (-) button decreases quantity by 1 down to minimum of 0
* **Standard Packaging Process Access** When the user taps "Standard packaging process" option:
  * The system navigates to Standard packaging process screen
  * The screen displays list of packaging categories with icons:
    * "Liquid goods (cosmetics, shampoo, etc.)"
    * "Clothing, handbags, shoes"
    * "Books, documents, and office supplies"
    * "Fragile items (cups, plates, glassware, etc.)"
    * "Laptop, computer monitor, TV screen,..."
    * "Electronics, home appliances, and refrigeration appliances"
    * "Bulky furniture items"
  * Each category shows right arrow indicating detailed information available
* **Validation and Constraints** The system enforces the following validation rules:
  * Maximum quantity of 20 for each furniture category
  * Total disassembly service quantities must be less than Electronic devices main category quantity
  * Categories automatically collapse when quantity reaches 0
  * Photo capture remains optional and does not block progression

### **6.4 Choose Working Time**

* **Display 14-Day Calendar**
  * When the user opens the **Choose Working Time** screen, the system displays a **14-day horizontal or grid calendar**, starting from **today**.
  * The **first two days** (i.e., **today and tomorrow**) are **shown but grayed out and untappable**.
  * From **Day 3 onward**, each date is **tap-enabled**.
  * When a user taps on a selectable date, that date is **highlighted** to indicate it is chosen.
  * Only **one date can be selected at a time**.
* **Time Picker Behavior**
  * Below the calendar, a **scrollable time selector** is shown with hour and minute options.
  * The user can scroll to choose a time in **HH:MM AM/PM format**.
  * The default time may be unselected or set to a common default (e.g. 9:00 AM), but users can freely change it. The user can only choose from 6AM to 10PM
* **Dynamic Price and Duration Update**
  * When the user selects a valid **date and time**, the **total price and estimated task duration** displayed at the bottom update accordingly.
  * If no time is selected, this section may remain in a default state or hidden.
* **Notes for Tasker Input**
  * A **text input area** labeled "Notes for Tasker" is available below the date and time pickers.
  * The field is **optional** and **accepts free-form text input up to 400 characters**.
  * Users may leave this field empty.
  * When the character count reaches 400, the input field prevents further typing.

### 6.5 Overview

* **Address Information Display and Editing**
  * "Address" section displays with complete current location and new location information
  * "Current location" shows:
    * Complete address with full street address, district, city, and country
    * Property type showing selected property classification and area/floor configuration
    * Transportation details showing staircase transportation floor count when selected
    * Alley specification showing distance and width when configured
    * Additional services like "Transportation from the parking garage" when enabled
    * Green edit icon positioned on right side of "Current location" label
  * "New location" shows:
    * Complete address with full street address, district, city, and country
    * Property type showing selected property classification and area/floor configuration
    * Transportation details showing staircase transportation floor count when selected
    * Alley specification showing distance and width when configured
    * Additional services like "Transportation from the parking garage" when enabled
    * Green edit icon positioned on right side of "New location" label
  * When "The same building" option was selected, new location shows same address as current location
  * When user taps edit icon next to "Current location", system navigates back to Current Location configuration screen
  * When user taps edit icon next to "New location", system navigates back to New Location configuration screen
* **Furniture Inventory Display and Modification**
  * "Oversized furniture" section displays with green edit icon on right side of section header
  * Furniture categories show in list format with quantity indicators:
    * "Electronic devices" with quantity indicator on right when selected
    * "Removable furniture" with quantity indicator on right when selected
    * "Solid furniture" with quantity indicator on right when selected
  * Only selected furniture categories with quantities greater than 0 are displayed
  * When user taps green edit icon next to "Oversized furniture", system navigates back to Select items to be transported screen
* **Working Time Information Display and Editing**
  * "Working time" section displays with green edit icon on right side of section header
  * Service type shows "Home moving" on left side
  * Selected date and time shows on right side in format HH:MM AM/PM - MM/DD/YYYY
  * When user taps green edit icon next to "Working time", system navigates back to Choose Working Time screen
* **Price Display and Next Button Functionality**
  * Price reflects sum of all selected services including property types, transportation options, furniture quantities, and scheduling preferences
  * When user taps anywhere on the green price button, system validates all information is complete and navigates to Confirm and Payment screen
* **Screen Scroll and Information Access**
  * Screen content is scrollable when information exceeds screen height
  * Back arrow in header navigates user to previous step (Choose Working Time)
  * Information icon in header provides access to additional help or service details when tapped
  * All edit icons maintain consistent green styling and positioning for easy identification and access

### **6.6 Confirm & Payment**

* The booking summary displays all essential details, including:
  * Location of service.
  * Client contact information with "Change" button.
  * Selected date, time, and estimated duration of the task.
  * Description of the task.
  * Payment method options: Cash option with icon, Promotion option with icon.
* **Payment Method Selection:**
  * **Supported Methods**: The system supports the following payment methods:
    * Cash
    * Electronic payments (bPay, Momo, ZaloPay, ShopeePay, VietQR, VNPAY, Visa/Master, Pay later/Installment via Kredivo)
  * **Selection Flow**:
    * When user opens the Payment Method screen, all available methods are displayed.
    * Once a method is selected, the system stores the selection and navigates the user back to the **Confirm & Pay** screen.
    * If Visa/Master is selected and no card is saved, the system navigates to the **Add Card** screen for user input.
* **Promotion Code Handling**:
  * User may enter a promotion code or select from available vouchers.
  * If a valid promotion code is applied, the system:
    * Updates the discount
    * Recalculates the total price in the booking summary
    * Displays the promotion code and corresponding discount value
  * If the promotion code is invalid:
    * A notification is shown: **“The promotion code is invalid. Please check again.”**
    * No price change is applied
* Final total displayed prominently.
* "Book" button available for final confirmation only when all required information is complete.
* Upon tapping **“Book”**, system processes the booking and navigates the user to a **Booking Confirmation screen** upon success.

### **6.7 Error & Edge Cases**

* Past date/time selection shows appropriate error message.
* Incomplete address fields prevent progression to the next step.
* Invalid or unsupported address shows validation error.
* Invalid contact information displays validation errors.
* Network errors during booking show retry options.
* Payment method selection required before final booking.

## **7. Open Questions / Assumptions**

* What users can edit in their bookings after confirmation? What will happen if their edit makes the total price lower?
* What is the process if a user cancels before scheduled time?
* Should the user only be able to book tasks within the current week?
* Max number of promo codes per booking?
* Price and time increments for each add-on are clearly defined in the business logic.
* Fees for manual tasker selection and rules are transparent and consistently enforced.
